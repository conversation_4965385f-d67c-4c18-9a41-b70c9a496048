"""
Simple test to record real audio and compare with/without noise suppression
"""
import numpy as np
import sys
import sounddevice as sd
import wave
import time
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent)
sys.path.append(project_root)

from core.audio.noise_suppressor import get_noise_suppressor

def create_test_signals():
    """Create simple test audio signals"""
    sample_rate = 48000  # RNNoise works at 48kHz
    duration = 0.5  # 500ms
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 1. Pure silence
    silence = np.zeros(len(t), dtype=np.int16)
    
    # 2. White noise
    noise = np.random.normal(0, 0.3, len(t))
    white_noise = (noise * 1000).astype(np.int16)
    
    # 3. Speech-like signal (multiple harmonics)
    speech = (
        np.sin(2 * np.pi * 200 * t) +  # 200 Hz fundamental
        0.7 * np.sin(2 * np.pi * 400 * t) +  # 400 Hz harmonic
        0.5 * np.sin(2 * np.pi * 800 * t) +  # 800 Hz harmonic
        0.2 * np.random.normal(0, 1, len(t))  # Natural variation
    )
    speech_signal = (speech * 2000).astype(np.int16)
    
    # 4. High frequency noise (should be filtered)
    high_freq = np.sin(2 * np.pi * 8000 * t)  # 8kHz
    high_freq_noise = (high_freq * 1500).astype(np.int16)
    
    return {
        "silence": silence,
        "white_noise": white_noise, 
        "speech_signal": speech_signal,
        "high_freq_noise": high_freq_noise
    }

def select_microphone():
    """Let user select microphone device"""
    print("\n🎤 Available Audio Input Devices:")
    devices = sd.query_devices()
    input_devices = []

    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            input_devices.append((i, device))
            print(f"  {len(input_devices)}. {device['name']} (Device {i})")

    if not input_devices:
        print("❌ No input devices found!")
        return None

    print(f"  0. Use Default Microphone")

    while True:
        try:
            choice = input(f"Select microphone (0 for default, 1-{len(input_devices)}): ").strip()
            if choice == "0" or choice == "":
                return None  # Use default

            device_num = int(choice)
            if 1 <= device_num <= len(input_devices):
                device_index = input_devices[device_num - 1][0]
                device_name = input_devices[device_num - 1][1]['name']
                print(f"✅ Selected: {device_name}")
                return device_index
            else:
                print(f"Please enter 0 or a number between 1 and {len(input_devices)}")
        except ValueError:
            print("Please enter a valid number")
        except KeyboardInterrupt:
            return None

def record_audio(device_index, duration=5, sample_rate=16000):
    """Record audio from microphone"""
    print(f"🔴 Recording for {duration} seconds... Speak now!")

    # Record audio
    audio_data = sd.rec(int(duration * sample_rate),
                       samplerate=sample_rate,
                       channels=1,
                       dtype='int16',
                       device=device_index)
    sd.wait()  # Wait for recording to complete

    return audio_data.flatten()

def save_audio_file(audio_samples, sample_rate, filename):
    """Save audio samples to WAV file"""
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_samples.tobytes())

def test_real_audio_suppression():
    """Test noise suppressor with real recorded audio"""
    print("🎯 Real Audio Noise Suppression Test")
    print("=" * 60)

    try:
        # Test with default config
        print("📋 Testing with DEFAULT config:")
        suppressor_default = get_noise_suppressor("real_audio_test")
        print(f"   Enabled: {suppressor_default.config.enabled}")
        print(f"   Noise strength: {suppressor_default.config.noise_strength}")
        print(f"   Bandpass: {suppressor_default.config.bandpass_low}-{suppressor_default.config.bandpass_high}Hz")

        # Test with banking workflow config
        print("\n📋 Testing with BANKING WORKFLOW config:")
        from core.config.interrupt_config import get_interrupt_config
        import json

        # Load banking workflow
        with open("workflows/banking_workflow_v2.json", "r") as f:
            banking_workflow = json.load(f)

        banking_interrupt_config = get_interrupt_config(banking_workflow)
        suppressor_banking = get_noise_suppressor("banking_test", interrupt_config=banking_interrupt_config)
        print(f"   Enabled: {suppressor_banking.config.enabled}")
        print(f"   Noise strength: {suppressor_banking.config.noise_strength}")
        print(f"   Bandpass: {suppressor_banking.config.bandpass_low}-{suppressor_banking.config.bandpass_high}Hz")

        # Use the banking workflow suppressor for testing
        suppressor = suppressor_banking
        print(f"\n✅ Using BANKING WORKFLOW noise suppressor for test")
        print()

        # Select microphone
        device_index = select_microphone()
        if device_index is None and len(sd.query_devices()) == 0:
            print("❌ No audio devices available")
            return False

        # Recording parameters
        sample_rate = 16000  # Standard rate for speech processing
        duration = 5  # 5 seconds

        print(f"\n📋 Test Instructions:")
        print(f"   🎤 You will record {duration} seconds of audio")
        print(f"   🗣️ Try speaking with some background noise (tap desk, rustle papers, etc.)")
        print(f"   📊 We'll compare the audio with and without noise suppression")

        input("\n⏸️ Press Enter when ready to record...")

        # Record audio
        print(f"\n🎬 Recording audio...")
        audio_samples = record_audio(device_index, duration, sample_rate)

        print(f"✅ Recording completed! ({len(audio_samples)} samples)")

        # Save original audio
        original_filename = "original_audio.wav"
        save_audio_file(audio_samples, sample_rate, original_filename)
        print(f"💾 Original audio saved as: {original_filename}")

        # Process with noise suppression
        print(f"\n🔧 Processing with noise suppression...")
        start_time = time.time()
        filtered_samples, info = suppressor.process_audio_sync(audio_samples, sample_rate)
        processing_time = time.time() - start_time

        # Save filtered audio
        filtered_filename = "filtered_audio.wav"
        save_audio_file(filtered_samples, sample_rate, filtered_filename)
        print(f"💾 Filtered audio saved as: {filtered_filename}")

        # Calculate statistics
        original_energy = np.mean(audio_samples.astype(np.float64) ** 2)
        filtered_energy = np.mean(filtered_samples.astype(np.float64) ** 2)
        original_rms = np.sqrt(original_energy)
        filtered_rms = np.sqrt(filtered_energy)

        if original_energy > 0:
            suppression_ratio = (original_energy - filtered_energy) / original_energy
            suppression_db = 20 * np.log10(filtered_rms / original_rms) if original_rms > 0 else -100
        else:
            suppression_ratio = 0
            suppression_db = 0

        # Display results
        print(f"\n📊 COMPARISON RESULTS:")
        print(f"=" * 60)
        print(f"🎤 Original Audio:")
        print(f"   RMS Energy: {original_rms:.1f}")
        print(f"   File: {original_filename}")

        print(f"\n🔧 Filtered Audio:")
        print(f"   RMS Energy: {filtered_rms:.1f}")
        print(f"   File: {filtered_filename}")
        print(f"   Processing time: {info.get('processing_time_ms', 0):.1f}ms")
        print(f"   Noise suppression applied: {info.get('noise_suppression_applied', False)}")

        print(f"\n📈 Noise Reduction:")
        print(f"   Energy reduction: {suppression_ratio*100:.1f}%")
        print(f"   Decibel change: {suppression_db:.1f} dB")

        # Interpretation
        if suppression_ratio > 0.3:
            print(f"   💡 Strong noise reduction - significant background noise removed")
        elif suppression_ratio > 0.1:
            print(f"   💡 Moderate noise reduction - some background noise removed")
        elif suppression_ratio > 0.02:
            print(f"   💡 Light noise reduction - minor cleanup applied")
        else:
            print(f"   💡 Minimal change - audio was already clean or suppression disabled")

        print(f"\n🎧 Listen to both files to hear the difference!")
        print(f"   Original: {original_filename}")
        print(f"   Filtered: {filtered_filename}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run the real audio test instead of synthetic signals
    test_real_audio_suppression()
