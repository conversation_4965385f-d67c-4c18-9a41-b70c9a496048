"""
Simple test to check what the noise suppressor is doing
"""
import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent)
sys.path.append(project_root)

from core.audio.noise_suppressor import get_noise_suppressor

def create_test_signals():
    """Create simple test audio signals"""
    sample_rate = 48000  # RNNoise works at 48kHz
    duration = 0.5  # 500ms
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 1. Pure silence
    silence = np.zeros(len(t), dtype=np.int16)
    
    # 2. White noise
    noise = np.random.normal(0, 0.3, len(t))
    white_noise = (noise * 1000).astype(np.int16)
    
    # 3. Speech-like signal (multiple harmonics)
    speech = (
        np.sin(2 * np.pi * 200 * t) +  # 200 Hz fundamental
        0.7 * np.sin(2 * np.pi * 400 * t) +  # 400 Hz harmonic
        0.5 * np.sin(2 * np.pi * 800 * t) +  # 800 Hz harmonic
        0.2 * np.random.normal(0, 1, len(t))  # Natural variation
    )
    speech_signal = (speech * 2000).astype(np.int16)
    
    # 4. High frequency noise (should be filtered)
    high_freq = np.sin(2 * np.pi * 8000 * t)  # 8kHz
    high_freq_noise = (high_freq * 1500).astype(np.int16)
    
    return {
        "silence": silence,
        "white_noise": white_noise, 
        "speech_signal": speech_signal,
        "high_freq_noise": high_freq_noise
    }

def test_noise_suppressor():
    """Test the noise suppressor with different signals"""
    print("🎯 Simple Noise Suppressor Test")
    print("=" * 50)
    
    try:
        # Get noise suppressor
        suppressor = get_noise_suppressor("simple_test")
        print(f"✅ Noise suppressor created")
        print(f"   Enabled: {suppressor.config.enabled}")
        print(f"   Noise strength: {suppressor.config.noise_strength}")
        print(f"   Bandpass: {suppressor.config.bandpass_low}-{suppressor.config.bandpass_high}Hz")
        print()
        
        # Create test signals
        test_signals = create_test_signals()
        sample_rate = 48000
        
        print("🧪 Testing different audio signals:")
        print("-" * 50)
        
        for signal_name, audio_samples in test_signals.items():
            print(f"\n📊 Testing: {signal_name}")
            
            # Calculate original energy
            original_energy = np.mean(audio_samples.astype(np.float64) ** 2)
            original_rms = np.sqrt(original_energy)
            
            # Process through noise suppressor
            filtered_samples, info = suppressor.process_audio_sync(audio_samples, sample_rate)
            
            # Calculate filtered energy
            filtered_energy = np.mean(filtered_samples.astype(np.float64) ** 2)
            filtered_rms = np.sqrt(filtered_energy)
            
            # Calculate suppression
            if original_energy > 0:
                suppression_ratio = (original_energy - filtered_energy) / original_energy
                suppression_db = 20 * np.log10(filtered_rms / original_rms) if original_rms > 0 else -100
            else:
                suppression_ratio = 0
                suppression_db = 0
            
            print(f"   Original RMS: {original_rms:.1f}")
            print(f"   Filtered RMS: {filtered_rms:.1f}")
            print(f"   Suppression: {suppression_ratio*100:.1f}% ({suppression_db:.1f} dB)")
            print(f"   Processing time: {info.get('processing_time_ms', 0):.1f}ms")
            print(f"   Noise suppression applied: {info.get('noise_suppression_applied', False)}")
            
            # Interpretation
            if suppression_ratio > 0.5:
                print(f"   💡 Strong suppression - likely noise")
            elif suppression_ratio > 0.2:
                print(f"   💡 Moderate suppression - some noise removed")
            elif suppression_ratio > 0.05:
                print(f"   💡 Light suppression - minor cleanup")
            else:
                print(f"   💡 Minimal change - signal preserved")
        
        print("\n" + "=" * 50)
        print("✅ Test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_noise_suppressor()
