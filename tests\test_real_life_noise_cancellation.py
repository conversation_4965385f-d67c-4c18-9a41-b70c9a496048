"""
🎯 REAL-LIFE NOISE CANCELLATION & INTERRUPT TEST
================================================

Test the noise suppression system with real microphone input and interrupt detection.
This test simulates real-world usage scenarios.
"""

import asyncio
import numpy as np
import sounddevice as sd
import time
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from utils.audio_utils import record_microphone_audio_vad, enhanced_vad_check
from core.audio.noise_suppressor import get_noise_suppressor
from core.interruption.interrupt_handler import InterruptHandler
from core.config.interrupt_config import get_interrupt_config


async def select_input_device():
    """Select audio input device for microphone recording."""
    print("\n🎤 Available Audio Input Devices:")
    devices = sd.query_devices()
    input_devices = []
    
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            input_devices.append((i, device))
            print(f"  {len(input_devices)}. {device['name']} (Device {i})")
    
    if not input_devices:
        print("❌ No input devices found!")
        return None
    
    # Add default option
    print(f"  0. Use Default Microphone (recommended)")

    while True:
        try:
            choice = input(f"Select input device (0 for default, 1-{len(input_devices)}): ").strip()

            if choice == "0" or choice == "":
                print("✅ Selected: Default microphone device")
                return None  # None means use default device

            device_num = int(choice)
            if 1 <= device_num <= len(input_devices):
                device_index = input_devices[device_num - 1][0]
                device_name = input_devices[device_num - 1][1]['name']
                print(f"✅ Selected: {device_name}")
                return device_index
            else:
                print(f"Please enter 0 for default or a number between 1 and {len(input_devices)}")
        except ValueError:
            print("Please enter a valid number or 0 for default")
        except KeyboardInterrupt:
            print("\n❌ Device selection cancelled")
            return None


async def test_noise_suppression_recording():
    """Test noise suppression with real microphone recording"""
    print("🎯 TEST 1: Real Microphone Recording with Noise Suppression")
    print("=" * 70)
    
    try:
        # Select input device
        device_index = await select_input_device()
        if device_index is None and len(sd.query_devices()) == 0:
            print("❌ No audio devices available, skipping test")
            return False
        
        print("\n📋 Test Instructions:")
        print("   1. First recording: Stay SILENT (test background noise filtering)")
        print("   2. Second recording: Speak CLEARLY (test speech detection)")
        print("   3. Third recording: Speak with BACKGROUND NOISE (test noise cancellation)")
        print("   4. Each recording: 15 seconds max, 3 seconds silence to stop")
        
        # Test scenarios
        scenarios = [
            {
                "name": "Background Noise Only",
                "instruction": "Stay SILENT - let's capture background noise",
                "expected": "Should detect NO speech"
            },
            {
                "name": "Clear Speech",
                "instruction": "Speak CLEARLY - say something like 'Hello, this is a test'",
                "expected": "Should detect speech clearly"
            },
            {
                "name": "Speech with Background Noise",
                "instruction": "Speak while making background noise (tap desk, rustle papers, etc.)",
                "expected": "Should detect speech despite noise"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n🎬 SCENARIO {i}: {scenario['name']}")
            print(f"   📢 {scenario['instruction']}")
            print(f"   🎯 Expected: {scenario['expected']}")
            
            input("   ⏸️ Press Enter when ready to start recording...")
            
            # Record with VAD (uses noise suppression internally)
            print("   🔴 RECORDING... (speak now!)")
            start_time = time.time()
            
            user_audio_path = await record_microphone_audio_vad(
                device_index=device_index,
                silence_duration=3.0,  # Allow 3 seconds of silence before stopping
                max_recording=15.0     # Maximum 15 seconds of recording
            )
            
            recording_duration = time.time() - start_time
            
            if user_audio_path:
                print(f"   ✅ Recording completed: {user_audio_path}")
                print(f"   ⏱️ Duration: {recording_duration:.1f} seconds")
                
                # Analyze the recorded audio
                try:
                    import wave
                    with wave.open(user_audio_path, 'rb') as wav_file:
                        frames = wav_file.readframes(-1)
                        sample_rate = wav_file.getframerate()
                        audio_samples = np.frombuffer(frames, dtype=np.int16)
                    
                    # Test with and without noise suppression
                    noise_suppressor = get_noise_suppressor(f"test_scenario_{i}")
                    
                    # Original audio analysis
                    vad_original = enhanced_vad_check(audio_samples, sample_rate)
                    
                    # Noise-suppressed audio analysis
                    filtered_audio, suppression_info = await noise_suppressor.process_audio_async(
                        audio_samples, sample_rate
                    )
                    vad_filtered = enhanced_vad_check(filtered_audio, sample_rate)
                    
                    result = {
                        "scenario": scenario['name'],
                        "duration": recording_duration,
                        "file_path": user_audio_path,
                        "original_energy": vad_original.get('energy', 0),
                        "filtered_energy": vad_filtered.get('energy', 0),
                        "original_vad": vad_original.get('has_voice', False),
                        "filtered_vad": vad_filtered.get('has_voice', False),
                        "noise_reduction_applied": suppression_info.get('noise_suppression_applied', False),
                        "processing_time": suppression_info.get('processing_time_ms', 0)
                    }
                    
                    results.append(result)
                    
                    print(f"   📊 Original energy: {result['original_energy']:.0f}")
                    print(f"   📊 Filtered energy: {result['filtered_energy']:.0f}")
                    print(f"   📊 Original VAD: {result['original_vad']}")
                    print(f"   📊 Filtered VAD: {result['filtered_vad']}")
                    print(f"   🔧 Noise suppression: {result['noise_reduction_applied']}")
                    print(f"   ⏱️ Processing time: {result['processing_time']:.1f}ms")
                    
                except Exception as e:
                    print(f"   ⚠️ Could not analyze audio file: {e}")
                    
            else:
                print("   ❌ No speech detected or recording failed")
                results.append({
                    "scenario": scenario['name'],
                    "duration": recording_duration,
                    "file_path": None,
                    "result": "No speech detected"
                })
            
            print()
        
        # Summary analysis
        print("🎯 RECORDING TEST SUMMARY")
        print("=" * 70)
        
        for i, result in enumerate(results, 1):
            if result.get('file_path'):
                energy_reduction = 0
                if result['original_energy'] > 0:
                    energy_reduction = ((result['original_energy'] - result['filtered_energy']) / result['original_energy']) * 100
                
                print(f"📊 Scenario {i}: {result['scenario']}")
                print(f"   Duration: {result['duration']:.1f}s")
                print(f"   Energy reduction: {energy_reduction:.1f}%")
                print(f"   VAD change: {result['original_vad']} → {result['filtered_vad']}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Recording test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_real_time_interrupt_detection():
    """Test real-time interrupt detection with noise suppression"""
    print("🎯 TEST 2: Real-Time Interrupt Detection")
    print("=" * 70)
    
    try:
        # Select input device
        device_index = await select_input_device()
        if device_index is None and len(sd.query_devices()) == 0:
            print("❌ No audio devices available, skipping test")
            return False
        
        # Create mock memory manager for interrupt handler
        class MockMemoryManager:
            def __init__(self):
                self.data = {}
            def get(self, key):
                return self.data.get(key)
            def set(self, key, value):
                self.data[key] = value
        
        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("real_time_test", memory_manager)

        # Get interrupt config
        config = get_interrupt_config()
        
        print("\n📋 Real-Time Interrupt Test Instructions:")
        print("   🎤 This will monitor your microphone for 30 seconds")
        print("   🗣️ Try speaking at different times to test interrupt detection")
        print("   🔇 Try staying silent to test noise filtering")
        print("   📢 Try making background noise without speaking")
        print("   ⏹️ Press Ctrl+C to stop early")
        
        input("   ⏸️ Press Enter to start real-time monitoring...")
        
        # Real-time monitoring parameters
        sample_rate = 16000
        chunk_duration = 0.1  # 100ms chunks
        chunk_size = int(sample_rate * chunk_duration)
        total_duration = 30.0  # 30 seconds
        total_chunks = int(total_duration / chunk_duration)
        
        print(f"\n🔴 MONITORING... (listening for {total_duration} seconds)")
        print("   💡 Speak to test interrupt detection!")
        
        interrupt_count = 0
        processing_times = []
        energy_levels = []
        
        try:
            for chunk_num in range(total_chunks):
                # Record chunk
                audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                sd.wait()
                audio_samples = audio_chunk.flatten()
                
                # Process through noise suppressor
                start_time = time.time()
                filtered_audio, suppression_info = await interrupt_handler.noise_suppressor.process_audio_async(
                    audio_samples, sample_rate
                )
                
                # Apply INTERRUPT-SPECIFIC VAD (not regular VAD)
                vad_result = interrupt_handler._interrupt_specific_vad(
                    filtered_audio,
                    sample_rate,
                    config.global_settings.vad_threshold
                )
                processing_time = (time.time() - start_time) * 1000
                
                processing_times.append(processing_time)
                energy_levels.append(vad_result.get('energy', 0))
                
                # Check for interrupt (voice detected)
                if vad_result.get('has_voice', False):
                    interrupt_count += 1
                    print(f"   🎤 INTERRUPT DETECTED! (#{interrupt_count}) - "
                          f"Energy: {vad_result.get('energy', 0):.0f}, "
                          f"Processing: {processing_time:.1f}ms")
                
                # Progress indicator every 5 seconds
                if (chunk_num + 1) % 50 == 0:
                    elapsed = (chunk_num + 1) * chunk_duration
                    print(f"   ⏱️ {elapsed:.0f}s elapsed... (interrupts detected: {interrupt_count})")
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.01)
                
        except KeyboardInterrupt:
            print("\n   ⏹️ Monitoring stopped by user")
        
        # Analysis
        avg_processing_time = np.mean(processing_times)
        max_processing_time = np.max(processing_times)
        avg_energy = np.mean(energy_levels)
        max_energy = np.max(energy_levels)
        
        print(f"\n📊 REAL-TIME MONITORING RESULTS:")
        print(f"   🎤 Total interrupts detected: {interrupt_count}")
        print(f"   ⏱️ Average processing time: {avg_processing_time:.1f}ms")
        print(f"   ⏱️ Maximum processing time: {max_processing_time:.1f}ms")
        print(f"   📊 Average energy level: {avg_energy:.0f}")
        print(f"   📊 Maximum energy level: {max_energy:.0f}")
        
        # Performance evaluation
        if avg_processing_time < 50:
            print("   ✅ Excellent real-time performance!")
        elif avg_processing_time < 100:
            print("   ✅ Good real-time performance")
        else:
            print("   ⚠️ Processing may be too slow for real-time")
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time interrupt test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_noise_suppression_comparison():
    """Test noise suppression ON vs OFF comparison"""
    print("🎯 TEST 3: Noise Suppression ON vs OFF Comparison")
    print("=" * 70)
    
    try:
        # Select input device
        device_index = await select_input_device()
        if device_index is None and len(sd.query_devices()) == 0:
            print("❌ No audio devices available, skipping test")
            return False
        
        print("\n📋 Comparison Test Instructions:")
        print("   🎤 Record the SAME speech twice:")
        print("   1️⃣ First: With noise suppression ON")
        print("   2️⃣ Second: With noise suppression OFF")
        print("   💡 Try to speak in the same way both times for fair comparison")
        
        # Get noise suppressor
        noise_suppressor = get_noise_suppressor("comparison_test")
        original_enabled = noise_suppressor.config.enabled
        
        results = []
        
        for test_num, (enabled, label) in enumerate([(True, "ON"), (False, "OFF")], 1):
            print(f"\n🎬 TEST {test_num}: Noise Suppression {label}")
            
            # Set noise suppression state
            noise_suppressor.config.enabled = enabled
            print(f"   🔧 Noise suppression: {label}")
            
            input("   ⏸️ Press Enter when ready to record...")
            
            # Record with VAD
            print("   🔴 RECORDING... (speak the same phrase!)")
            
            user_audio_path = await record_microphone_audio_vad(
                device_index=device_index,
                silence_duration=2.0,  # Shorter silence for comparison
                max_recording=10.0     # Shorter recording for comparison
            )
            
            if user_audio_path:
                print(f"   ✅ Recording completed: {user_audio_path}")
                
                # Analyze the recorded audio
                try:
                    import wave
                    with wave.open(user_audio_path, 'rb') as wav_file:
                        frames = wav_file.readframes(-1)
                        sample_rate = wav_file.getframerate()
                        audio_samples = np.frombuffer(frames, dtype=np.int16)
                    
                    # Analyze audio
                    vad_result = enhanced_vad_check(audio_samples, sample_rate)
                    
                    result = {
                        "noise_suppression": label,
                        "enabled": enabled,
                        "file_path": user_audio_path,
                        "energy": vad_result.get('energy', 0),
                        "has_voice": vad_result.get('has_voice', False),
                        "zero_crossing_rate": vad_result.get('zero_crossing_rate', 0),
                        "spectral_centroid": vad_result.get('spectral_centroid', 0)
                    }
                    
                    results.append(result)
                    
                    print(f"   📊 Energy: {result['energy']:.0f}")
                    print(f"   📊 VAD: {result['has_voice']}")
                    print(f"   📊 ZCR: {result['zero_crossing_rate']:.3f}")
                    print(f"   📊 Spectral centroid: {result['spectral_centroid']:.1f}Hz")
                    
                except Exception as e:
                    print(f"   ⚠️ Could not analyze audio: {e}")
            else:
                print("   ❌ No speech detected")
        
        # Restore original setting
        noise_suppressor.config.enabled = original_enabled
        
        # Comparison analysis
        if len(results) == 2:
            print("\n🎯 COMPARISON RESULTS")
            print("=" * 70)
            
            on_result = results[0] if results[0]['enabled'] else results[1]
            off_result = results[1] if not results[1]['enabled'] else results[0]
            
            energy_diff = on_result['energy'] - off_result['energy']
            energy_change = (energy_diff / off_result['energy']) * 100 if off_result['energy'] > 0 else 0
            
            print(f"📊 Energy Levels:")
            print(f"   Noise Suppression ON:  {on_result['energy']:.0f}")
            print(f"   Noise Suppression OFF: {off_result['energy']:.0f}")
            print(f"   Difference: {energy_diff:.0f} ({energy_change:+.1f}%)")
            
            print(f"\n📊 Voice Detection:")
            print(f"   Noise Suppression ON:  {on_result['has_voice']}")
            print(f"   Noise Suppression OFF: {off_result['has_voice']}")
            
            print(f"\n📊 Audio Quality Metrics:")
            print(f"   ZCR (ON/OFF): {on_result['zero_crossing_rate']:.3f} / {off_result['zero_crossing_rate']:.3f}")
            print(f"   Spectral Centroid (ON/OFF): {on_result['spectral_centroid']:.1f}Hz / {off_result['spectral_centroid']:.1f}Hz")
            
            # Interpretation
            if abs(energy_change) < 5:
                print("\n💡 Interpretation: Minimal difference (clean environment)")
            elif energy_change < -10:
                print("\n💡 Interpretation: Noise suppression reduced energy (filtered noise)")
            elif energy_change > 10:
                print("\n💡 Interpretation: Noise suppression may have enhanced signal")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all real-life noise cancellation tests"""
    print("🎯 REAL-LIFE NOISE CANCELLATION & INTERRUPT TESTS")
    print("=" * 80)
    print("Testing noise suppression with real microphone input...")
    print()
    
    # Check if audio devices are available
    try:
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        if not input_devices:
            print("❌ No audio input devices found. Please connect a microphone.")
            return
    except Exception as e:
        print(f"❌ Could not query audio devices: {e}")
        return
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Real Microphone Recording with Noise Suppression", test_noise_suppression_recording),
        ("Real-Time Interrupt Detection", test_real_time_interrupt_detection),
        ("Noise Suppression ON vs OFF Comparison", test_noise_suppression_comparison)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*80}")
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Final summary
    print("\n🎯 REAL-LIFE TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print()
    print(f"📊 RESULTS: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL REAL-LIFE TESTS PASSED!")
        print("The noise suppression system works great with real microphone input!")
    else:
        print("⚠️ Some real-life tests had issues. Check the output above.")
    
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
