"""
Interrupt Handling Configuration System

This module provides configurable settings for interrupt sensitivity,
confirmation requirements, and user preferences for the TTS interrupt system.
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from dotenv import load_dotenv

from core.logging.logger_config import get_module_logger

# Load environment variables
load_dotenv()

logger = get_module_logger("interrupt_config")


@dataclass
class GlobalInterruptSettings:
    """Global interrupt settings configuration."""
    enabled: bool = True
    vad_threshold: float = float(os.getenv('VAD_THRESHOLD', '50.0'))  # Ultra-high threshold for interrupt detection
    confirmation_window_seconds: float = 0.5
    min_interrupt_duration_seconds: float = 0.3
    tts_interrupt_cooldown_seconds: float = float(os.getenv('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0'))

    # Enhanced VAD parameters
    vad_method: str = os.getenv('VAD_METHOD', 'webrtcvad')
    webrtc_aggressiveness: int = int(os.getenv('WEBRTC_AGGRESSIVENESS', '3'))
    required_consecutive_frames: int = int(os.getenv('REQUIRED_CONSECUTIVE_FRAMES', '5'))
    user_speech_end_silence_seconds: float = float(os.getenv('USER_SPEECH_END_SILENCE_SECONDS', '1.5'))

    # Noise suppression parameters - OPTIMIZED FOR HUMAN-AI VOICE CHAT
    enable_noise_reduction: bool = os.getenv('ENABLE_NOISE_REDUCTION', 'true').lower() == 'true'  # Enable by default
    noise_reduction_strength: float = float(os.getenv('NOISE_REDUCTION_STRENGTH', '0.7'))  # Strong noise reduction
    bandpass_low_freq: float = float(os.getenv('BANDPASS_LOW_FREQ', '85.0'))  # Lower for male voices
    bandpass_high_freq: float = float(os.getenv('BANDPASS_HIGH_FREQ', '8000.0'))  # Higher for clarity

    # Enhanced VAD thresholds - TUNED FOR REAL HUMAN SPEECH vs AI VOICE
    min_speech_energy_ratio: float = float(os.getenv('MIN_SPEECH_ENERGY_RATIO', '0.25'))  # More sensitive
    max_zero_crossing_rate: float = float(os.getenv('MAX_ZERO_CROSSING_RATE', '0.35'))  # Allow natural variation
    min_spectral_centroid: float = float(os.getenv('MIN_SPECTRAL_CENTROID', '200.0'))  # Lower for deep voices
    max_spectral_centroid: float = float(os.getenv('MAX_SPECTRAL_CENTROID', '4000.0'))  # Higher for high voices


@dataclass
class InterruptConfig:
    """Simplified interrupt configuration with only global settings."""
    global_settings: GlobalInterruptSettings

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InterruptConfig':
        """Create configuration from dictionary."""
        interrupt_config_data = data.get('interrupt_config', data)
        return cls(
            global_settings=GlobalInterruptSettings(**interrupt_config_data.get('global_settings', {}))
        )

    # Backward compatibility properties
    @property
    def detection(self):
        """Backward compatibility property."""
        return self.global_settings


class InterruptConfigManager:
    """
    Manages interrupt configuration loading from workflow files.
    No longer uses standalone config files - all configuration comes from workflow JSON.
    """

    def __init__(self, config_dir: Optional[str] = None):
        self.logger = logger
        self._default_config = None

    def get_default_config(self) -> InterruptConfig:
        """Get the default interrupt configuration with environment variable overrides."""
        if self._default_config is None:
            self._default_config = self._create_default_config()
        return self._default_config

    def _create_default_config(self) -> InterruptConfig:
        """Create default configuration with environment variable overrides."""
        try:
            # Create configuration with environment variable overrides
            config = InterruptConfig(
                global_settings=GlobalInterruptSettings()
            )

            self.logger.info(
                "Default interrupt configuration created with environment variable overrides",
                action="_create_default_config",
                output_data={
                    "vad_threshold": config.global_settings.vad_threshold,
                    "tts_cooldown": config.global_settings.tts_interrupt_cooldown_seconds,
                    "vad_method": config.global_settings.vad_method
                },
                layer="interrupt_config"
            )

            return config
        except Exception as e:
            self.logger.error(
                "Error creating default configuration",
                action="_create_default_config",
                reason=str(e),
                layer="interrupt_config"
            )
            # Return configuration with environment variable overrides as fallback
            return InterruptConfig(
                global_settings=GlobalInterruptSettings()
            )

# Global configuration manager instance
_config_manager = None

def get_config_manager() -> InterruptConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = InterruptConfigManager()
    return _config_manager

def get_interrupt_config(workflow_config: Optional[Dict[str, Any]] = None) -> InterruptConfig:
    """
    Get interrupt configuration from workflow file with environment variable overrides.

    Priority order:
    1. Workflow configuration (from workflow JSON file)
    2. Environment variables
    3. Default values

    Args:
        workflow_config: Optional workflow configuration dict containing interrupt_config

    Returns:
        InterruptConfig: Configuration with workflow overrides and environment variable fallbacks
    """
    # Start with default configuration (includes environment variable overrides)
    default_config = get_config_manager().get_default_config()

    # If no workflow config provided, return default with environment overrides
    if not workflow_config:
        logger.info(
            "No workflow config provided, using default configuration with environment overrides",
            action="get_interrupt_config",
            layer="interrupt_config"
        )
        return default_config

    # Extract workflow interrupt config
    workflow_interrupt_config = workflow_config.get('workflow', {}).get('interrupt_config', {})
    if not workflow_interrupt_config:
        logger.info(
            "No interrupt_config found in workflow, using default configuration",
            action="get_interrupt_config",
            layer="interrupt_config"
        )
        return default_config

    # Merge workflow settings with defaults (including enhanced VAD parameters)
    workflow_global_settings = workflow_interrupt_config.get('global_settings', {})
    merged_settings = {
        'enabled': workflow_global_settings.get('enabled', default_config.global_settings.enabled),
        'vad_threshold': workflow_global_settings.get('vad_threshold', default_config.global_settings.vad_threshold),
        'confirmation_window_seconds': workflow_global_settings.get('confirmation_window_seconds', default_config.global_settings.confirmation_window_seconds),
        'min_interrupt_duration_seconds': workflow_global_settings.get('min_interrupt_duration_seconds', default_config.global_settings.min_interrupt_duration_seconds),
        'tts_interrupt_cooldown_seconds': workflow_global_settings.get('tts_interrupt_cooldown_seconds', default_config.global_settings.tts_interrupt_cooldown_seconds),

        # Enhanced VAD parameters
        'vad_method': workflow_global_settings.get('vad_method', default_config.global_settings.vad_method),
        'webrtc_aggressiveness': workflow_global_settings.get('webrtc_aggressiveness', default_config.global_settings.webrtc_aggressiveness),
        'required_consecutive_frames': workflow_global_settings.get('required_consecutive_frames', default_config.global_settings.required_consecutive_frames),
        'user_speech_end_silence_seconds': workflow_global_settings.get('user_speech_end_silence_seconds', default_config.global_settings.user_speech_end_silence_seconds),

        # Noise suppression parameters
        'enable_noise_reduction': workflow_global_settings.get('enable_noise_reduction', default_config.global_settings.enable_noise_reduction),
        'noise_reduction_strength': workflow_global_settings.get('noise_reduction_strength', default_config.global_settings.noise_reduction_strength),
        'bandpass_low_freq': workflow_global_settings.get('bandpass_low_freq', default_config.global_settings.bandpass_low_freq),
        'bandpass_high_freq': workflow_global_settings.get('bandpass_high_freq', default_config.global_settings.bandpass_high_freq),

        # Enhanced VAD thresholds
        'min_speech_energy_ratio': workflow_global_settings.get('min_speech_energy_ratio', default_config.global_settings.min_speech_energy_ratio),
        'max_zero_crossing_rate': workflow_global_settings.get('max_zero_crossing_rate', default_config.global_settings.max_zero_crossing_rate),
        'min_spectral_centroid': workflow_global_settings.get('min_spectral_centroid', default_config.global_settings.min_spectral_centroid),
        'max_spectral_centroid': workflow_global_settings.get('max_spectral_centroid', default_config.global_settings.max_spectral_centroid)
    }

    logger.info(
        "Using workflow interrupt configuration with environment variable fallbacks",
        action="get_interrupt_config",
        output_data={
            "enabled": merged_settings['enabled'],
            "vad_threshold": merged_settings['vad_threshold'],
            "vad_method": merged_settings['vad_method'],
            "workflow_overrides": len([k for k, v in workflow_global_settings.items() if v is not None])
        },
        layer="interrupt_config"
    )

    # Create new config with merged settings
    return InterruptConfig(
        global_settings=GlobalInterruptSettings(**merged_settings)
    )
