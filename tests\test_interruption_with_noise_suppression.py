"""
🎯 INTERRUPTION SYSTEM TEST WITH NOISE SUPPRESSION
==================================================

Test the interrupt handler with the new noise suppression system.
"""

import asyncio
import numpy as np
import time
import sounddevice as sd
from pathlib import Path
import sys
from unittest.mock import Mock, AsyncMock

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.interruption.interrupt_handler import InterruptHandler
from core.audio.noise_suppressor import get_noise_suppressor
from utils.audio_utils import enhanced_vad_check


class MockMemoryManager:
    """Mock memory manager for testing"""
    def __init__(self):
        self.data = {}
    
    def get(self, key):
        return self.data.get(key)
    
    def set(self, key, value):
        self.data[key] = value
    
    def clear(self, pattern=None):
        if pattern:
            keys_to_remove = [k for k in self.data.keys() if pattern in k]
            for key in keys_to_remove:
                del self.data[key]
        else:
            self.data.clear()


def create_interrupt_test_signals():
    """Create test signals for interrupt detection"""
    sample_rate = 16000
    duration = 0.5  # 500ms chunks
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 1. Silence (should NOT trigger interrupt)
    silence = np.zeros(len(t), dtype=np.int16)
    
    # 2. Background noise (should NOT trigger interrupt after filtering)
    noise = np.random.normal(0, 0.1, len(t))
    background_noise = (noise * 500).astype(np.int16)  # Reduced from 1000

    # 3. Human speech simulation (SHOULD trigger interrupt)
    # Multiple harmonics in human speech range - REALISTIC LEVELS
    speech = (
        np.sin(2 * np.pi * 200 * t) +  # 200 Hz (low male voice)
        0.7 * np.sin(2 * np.pi * 400 * t) +  # 400 Hz
        0.5 * np.sin(2 * np.pi * 800 * t) +  # 800 Hz
        0.3 * np.sin(2 * np.pi * 1200 * t) +  # 1200 Hz
        0.1 * np.random.normal(0, 1, len(t))  # Natural variation
    )
    human_speech = (speech * 2000).astype(np.int16)  # Reduced from 5000 to realistic level

    # 4. AI/TTS-like signal (should NOT trigger interrupt)
    # More consistent, less natural variation - REALISTIC LEVELS
    ai_voice = np.sin(2 * np.pi * 440 * t) + 0.3 * np.sin(2 * np.pi * 880 * t)
    ai_tts = (ai_voice * 3000).astype(np.int16)  # Reduced from 8000 to realistic level

    # 5. High frequency interference (should NOT trigger interrupt)
    high_freq = np.sin(2 * np.pi * 12000 * t)  # 12kHz - outside speech range
    interference = (high_freq * 2000).astype(np.int16)  # Reduced from 6000
    
    return {
        "silence": silence,
        "background_noise": background_noise,
        "human_speech": human_speech,
        "ai_tts": ai_tts,
        "interference": interference
    }


async def test_interrupt_handler_initialization():
    """Test interrupt handler initialization with noise suppression"""
    print("🎯 TEST 1: Interrupt Handler Initialization")
    print("=" * 60)
    
    try:
        # Create mock memory manager
        memory_manager = MockMemoryManager()
        
        # Create interrupt handler
        interrupt_handler = InterruptHandler("test_session", memory_manager)
        
        print("   ✅ InterruptHandler created successfully")
        
        # Check noise suppressor integration
        if hasattr(interrupt_handler, 'noise_suppressor'):
            ns = interrupt_handler.noise_suppressor
            print(f"   ✅ Noise suppressor integrated: {ns is not None}")
            print(f"   📋 Noise suppression enabled: {ns.config.enabled}")
            print(f"   📋 Noise strength: {ns.config.noise_strength}")
            print(f"   📋 Bandpass range: {ns.config.bandpass_low}-{ns.config.bandpass_high}Hz")
        else:
            print("   ❌ Noise suppressor not integrated")
            return False
        
        # Check interrupt config
        if hasattr(interrupt_handler, 'interrupt_config') and interrupt_handler.interrupt_config:
            config = interrupt_handler.interrupt_config
            print(f"   ✅ Interrupt config loaded: {config is not None}")
            print(f"   📋 VAD threshold: {config.global_settings.vad_threshold}")
            print(f"   📋 Interrupt enabled: {config.global_settings.enabled}")
        else:
            print("   ❌ Interrupt config not loaded")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print()


async def test_noise_suppression_in_interrupt_detection():
    """Test noise suppression integration in interrupt detection"""
    print("🎯 TEST 2: Noise Suppression in Interrupt Detection")
    print("=" * 60)
    
    try:
        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("test_session", memory_manager)
        
        # Get test signals
        test_signals = create_interrupt_test_signals()
        sample_rate = 16000
        
        results = {}
        
        for signal_name, audio_samples in test_signals.items():
            print(f"   🧪 Testing interrupt detection on {signal_name}...")
            
            # Test the noise suppression pipeline manually
            # (simulating what happens in the interrupt handler)
            
            # Step 1: Apply noise suppression
            filtered_samples, suppression_info = await interrupt_handler.noise_suppressor.process_audio_async(
                audio_samples, sample_rate
            )
            
            # Step 2: Apply enhanced VAD
            vad_result = enhanced_vad_check(
                filtered_samples, 
                sample_rate, 
                interrupt_handler.interrupt_config.global_settings.vad_threshold,
                apply_noise_suppression=False  # Already applied
            )
            
            # Calculate metrics
            original_energy = np.mean(audio_samples.astype(np.float64) ** 2)
            filtered_energy = vad_result.get('energy', 0)
            processing_time = suppression_info.get('processing_time_ms', 0)
            
            results[signal_name] = {
                "original_energy": original_energy,
                "filtered_energy": filtered_energy,
                "has_voice": vad_result.get('has_voice', False),
                "zero_crossing_rate": vad_result.get('zero_crossing_rate', 0),
                "spectral_centroid": vad_result.get('spectral_centroid', 0),
                "processing_time_ms": processing_time,
                "noise_suppression_applied": suppression_info.get('noise_suppression_applied', False)
            }
            
            print(f"      📊 Original energy: {original_energy:.0f}")
            print(f"      📊 Filtered energy: {filtered_energy:.0f}")
            print(f"      📊 VAD result: {vad_result.get('has_voice', False)}")
            print(f"      📊 ZCR: {vad_result.get('zero_crossing_rate', 0):.3f}")
            print(f"      📊 Spectral centroid: {vad_result.get('spectral_centroid', 0):.1f}Hz")
            print(f"      ⏱️ Processing time: {processing_time:.1f}ms")
            print(f"      🔧 Noise suppression: {suppression_info.get('noise_suppression_applied', False)}")

            # Add energy level interpretation
            if filtered_energy > 10000000:
                print(f"      ⚠️ Energy level very high (may indicate clipping)")
            elif filtered_energy > 1000000:
                print(f"      📢 Energy level high (loud speech)")
            elif filtered_energy > 100000:
                print(f"      🔊 Energy level normal (typical speech)")
            elif filtered_energy > 10000:
                print(f"      🔉 Energy level low (quiet speech)")
            else:
                print(f"      🔇 Energy level very low (background/silence)")
            print()
        
        # Analyze results
        print("   🔍 Analysis:")
        
        # Human speech should trigger interrupt
        if results["human_speech"]["has_voice"]:
            print("   ✅ Human speech correctly detected as voice")
        else:
            print("   ⚠️ Human speech not detected as voice")
        
        # Background noise should NOT trigger interrupt
        if not results["background_noise"]["has_voice"]:
            print("   ✅ Background noise correctly filtered out")
        else:
            print("   ⚠️ Background noise incorrectly detected as voice")
        
        # Silence should NOT trigger interrupt
        if not results["silence"]["has_voice"]:
            print("   ✅ Silence correctly ignored")
        else:
            print("   ⚠️ Silence incorrectly detected as voice")
        
        # AI/TTS should NOT trigger interrupt (ideally)
        if not results["ai_tts"]["has_voice"]:
            print("   ✅ AI/TTS voice correctly filtered out")
        else:
            print("   ⚠️ AI/TTS voice detected (may cause false interrupts)")
            print("      💡 Note: This is expected behavior - distinguishing AI from human voice is challenging")
        
        # High frequency interference should NOT trigger interrupt
        if not results["interference"]["has_voice"]:
            print("   ✅ High frequency interference correctly filtered out")
        else:
            print("   ⚠️ High frequency interference incorrectly detected as voice")
        
        # Processing should be fast
        avg_processing_time = np.mean([r["processing_time_ms"] for r in results.values()])
        if avg_processing_time < 30:
            print(f"   ✅ Fast processing: {avg_processing_time:.1f}ms average")
        else:
            print(f"   ⚠️ Slow processing: {avg_processing_time:.1f}ms average")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Noise suppression integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print()


async def test_interrupt_detection_methods():
    """Test different interrupt detection methods"""
    print("🎯 TEST 3: Interrupt Detection Methods")
    print("=" * 60)
    
    try:
        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("test_session", memory_manager)
        
        # Mock sounddevice for testing
        original_rec = sd.rec
        original_wait = sd.wait
        
        # Create test audio that should trigger interrupt
        test_signals = create_interrupt_test_signals()
        human_speech = test_signals["human_speech"]
        
        # Mock sd.rec to return our test audio
        def mock_rec(*args, **kwargs):
            return human_speech.reshape(-1, 1)
        
        def mock_wait():
            pass
        
        sd.rec = mock_rec
        sd.wait = mock_wait
        
        try:
            print("   🧪 Testing simple microphone check...")

            # Test simple microphone check (this should detect the human speech)
            # Note: This is a simplified test since we're mocking the audio input
            result = await interrupt_handler._simple_microphone_check()

            print(f"      📊 Simple check result: {result}")

            if hasattr(interrupt_handler, 'user_interrupt_input'):
                print(f"      📝 Captured input: {interrupt_handler.user_interrupt_input}")

            print("   ✅ Simple microphone check completed")
            
        finally:
            # Restore original functions
            sd.rec = original_rec
            sd.wait = original_wait
        
        return True
        
    except Exception as e:
        print(f"   ❌ Interrupt detection methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print()


async def test_performance_with_real_time_constraints():
    """Test performance under real-time constraints"""
    print("🎯 TEST 4: Real-Time Performance")
    print("=" * 60)
    
    try:
        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("test_session", memory_manager)
        
        # Simulate real-time audio chunks (30ms each)
        sample_rate = 16000
        chunk_duration = 0.03  # 30ms
        chunk_size = int(sample_rate * chunk_duration)
        num_chunks = 100  # 3 seconds of audio
        
        print(f"   🧪 Processing {num_chunks} real-time chunks...")
        
        processing_times = []
        vad_results = []
        
        for i in range(num_chunks):
            # Generate test chunk (mix of speech and noise)
            if i % 10 < 3:  # 30% speech-like chunks
                chunk = create_interrupt_test_signals()["human_speech"][:chunk_size]
            else:  # 70% noise/silence chunks
                chunk = create_interrupt_test_signals()["background_noise"][:chunk_size]
            
            # Measure processing time
            start_time = time.time()
            
            # Process through noise suppressor
            filtered_chunk, suppression_info = await interrupt_handler.noise_suppressor.process_audio_async(
                chunk, sample_rate
            )
            
            # Apply VAD
            vad_result = enhanced_vad_check(filtered_chunk, sample_rate)
            
            processing_time = (time.time() - start_time) * 1000  # ms
            processing_times.append(processing_time)
            vad_results.append(vad_result.get('has_voice', False))
            
            # Progress indicator
            if (i + 1) % 20 == 0:
                print(f"      Processed {i + 1}/{num_chunks} chunks...")
        
        # Calculate statistics
        avg_time = np.mean(processing_times)
        max_time = np.max(processing_times)
        p95_time = np.percentile(processing_times, 95)
        voice_detection_rate = np.mean(vad_results) * 100
        
        print(f"   📊 Average processing time: {avg_time:.1f}ms")
        print(f"   📊 Maximum processing time: {max_time:.1f}ms")
        print(f"   📊 95th percentile: {p95_time:.1f}ms")
        print(f"   📊 Voice detection rate: {voice_detection_rate:.1f}%")
        
        # Real-time performance evaluation
        # For 30ms chunks, processing should be much faster than 30ms
        if avg_time < 10:
            print("   ✅ Excellent real-time performance!")
        elif avg_time < 20:
            print("   ✅ Good real-time performance")
        elif avg_time < 30:
            print("   ⚠️ Acceptable real-time performance")
        else:
            print("   ❌ Too slow for real-time processing")
        
        # Check for consistent performance
        if max_time < 50:
            print("   ✅ Consistent performance (no major spikes)")
        else:
            print("   ⚠️ Performance spikes detected")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Real-time performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print()


async def test_interrupt_configuration_effects():
    """Test how different configurations affect interrupt detection"""
    print("🎯 TEST 5: Configuration Effects")
    print("=" * 60)
    
    try:
        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("test_session", memory_manager)
        
        # Get test signal
        test_signals = create_interrupt_test_signals()
        human_speech = test_signals["human_speech"]
        sample_rate = 16000
        
        # Test with different VAD thresholds
        thresholds = [0.01, 0.05, 0.1, 0.2]
        
        print("   🧪 Testing different VAD thresholds...")
        
        for threshold in thresholds:
            # Process through noise suppressor
            filtered_audio, _ = await interrupt_handler.noise_suppressor.process_audio_async(
                human_speech, sample_rate
            )
            
            # Apply VAD with different threshold
            vad_result = enhanced_vad_check(filtered_audio, sample_rate, threshold)
            
            print(f"      📊 Threshold {threshold}: VAD={vad_result.get('has_voice', False)}, "
                  f"Energy={vad_result.get('energy', 0):.0f}")
        
        # Test noise suppression on/off effect
        print("   🧪 Testing noise suppression on/off...")
        
        # Get original config state
        original_enabled = interrupt_handler.noise_suppressor.config.enabled
        
        # Test with noise suppression ON
        interrupt_handler.noise_suppressor.config.enabled = True
        filtered_on, info_on = await interrupt_handler.noise_suppressor.process_audio_async(
            human_speech, sample_rate
        )
        vad_on = enhanced_vad_check(filtered_on, sample_rate)
        
        # Test with noise suppression OFF
        interrupt_handler.noise_suppressor.config.enabled = False
        filtered_off, info_off = await interrupt_handler.noise_suppressor.process_audio_async(
            human_speech, sample_rate
        )
        vad_off = enhanced_vad_check(human_speech, sample_rate)  # Use original audio
        
        # Restore original state
        interrupt_handler.noise_suppressor.config.enabled = original_enabled
        
        print(f"      📊 Noise suppression ON: VAD={vad_on.get('has_voice', False)}, "
              f"Energy={vad_on.get('energy', 0):.0f}")
        print(f"      📊 Noise suppression OFF: VAD={vad_off.get('has_voice', False)}, "
              f"Energy={vad_off.get('energy', 0):.0f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration effects test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print()


async def main():
    """Run all interruption tests"""
    print("🎯 INTERRUPTION SYSTEM TESTS WITH NOISE SUPPRESSION")
    print("=" * 70)
    print("Testing interrupt detection with the new noise suppression system...")
    print()
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Interrupt Handler Initialization", test_interrupt_handler_initialization),
        ("Noise Suppression in Interrupt Detection", test_noise_suppression_in_interrupt_detection),
        ("Interrupt Detection Methods", test_interrupt_detection_methods),
        ("Real-Time Performance", test_performance_with_real_time_constraints),
        ("Configuration Effects", test_interrupt_configuration_effects)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("🎯 INTERRUPTION TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print()
    print(f"📊 RESULTS: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL INTERRUPTION TESTS PASSED!")
        print("The interrupt system is working correctly with noise suppression!")
    else:
        print("⚠️ Some interruption tests failed. Please check the output above.")
    
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
