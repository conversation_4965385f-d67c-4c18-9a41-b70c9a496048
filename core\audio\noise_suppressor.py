import numpy as np
import time
import noisereduce as nr
from core.config.interrupt_config import get_interrupt_config
from core.logging.logger_config import get_module_logger

# Global instance cache for noise suppressors
_noise_suppressor_cache = {}

class NoiseSuppressionConfig:
    """Configuration object for noise suppression settings"""
    def __init__(self, interrupt_config=None):
        if interrupt_config:
            self.enabled = interrupt_config.global_settings.enable_noise_reduction
            self.noise_strength = interrupt_config.global_settings.noise_reduction_strength
            self.bandpass_low = interrupt_config.global_settings.bandpass_low_freq
            self.bandpass_high = interrupt_config.global_settings.bandpass_high_freq
        else:
            # Fallback defaults
            self.enabled = True
            self.noise_strength = 0.6
            self.bandpass_low = 300.0
            self.bandpass_high = 3400.0

class NoiseSuppressor:
    def __init__(self, session_id=None):
        cfg = get_interrupt_config()
        self.config = NoiseSuppressionConfig(cfg)
        self.enabled = self.config.enabled
        self.logger = get_module_logger("noise_suppressor", session_id=session_id)
        self.session_id = session_id
        self.control_level = 50  # Default control level for rnnoise-cli

        if self.enabled:
            try:
                # Test if rnnoise-cli is available
                result = subprocess.run(['rnnoise', '--help'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    self.logger.info("rnnoise-cli is available for noise suppression")
                else:
                    raise Exception("rnnoise-cli not working properly")
            except Exception as e:
                self.enabled = False
                self.config.enabled = False
                self.logger.error(f"rnnoise-cli init failed: {e}")
                self.logger.info("Noise suppression disabled - rnnoise-cli not available")

    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using rnnoise-cli for noise suppression"""
        if not self.enabled:
            return audio_samples, {"noise_suppression_applied": False}

        try:
            # Convert to int16 if needed
            if audio_samples.dtype != np.int16:
                audio_samples = audio_samples.astype(np.int16)

            start_time = time.perf_counter()

            # Create temporary files for input and output
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as input_file, \
                 tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as output_file:

                input_path = input_file.name
                output_path = output_file.name

            try:
                # Write input audio to WAV file
                with wave.open(input_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_samples.tobytes())

                # Process with rnnoise-cli
                # Note: rnnoise-cli is designed for real-time audio streams,
                # so we'll use a simple approach for file processing
                cmd = [
                    'rnnoise', 'process',
                    '--input', input_path,
                    '--output', output_path,
                    '--control', str(self.control_level)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode == 0 and os.path.exists(output_path):
                    # Read processed audio
                    with wave.open(output_path, 'rb') as wav_file:
                        frames = wav_file.readframes(-1)
                        processed_samples = np.frombuffer(frames, dtype=np.int16)

                    proc_time = (time.perf_counter() - start_time) * 1000

                    return processed_samples, {
                        "noise_suppression_applied": True,
                        "processing_time_ms": proc_time,
                        "frame_size": len(audio_samples),
                        "control_level": self.control_level
                    }
                else:
                    self.logger.warning(f"rnnoise-cli failed: {result.stderr}")
                    return audio_samples, {"noise_suppression_applied": False}

            finally:
                # Clean up temporary files
                for path in [input_path, output_path]:
                    if os.path.exists(path):
                        os.unlink(path)

        except Exception as e:
            self.logger.error(f"rnnoise-cli processing error: {e}", exc_info=True)
            return audio_samples, {"noise_suppression_applied": False}

    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int):
        """Async interface for compatibility with VAD system"""
        return self.process_audio_sync(audio_samples, sample_rate)


def get_noise_suppressor(session_id: str = None) -> NoiseSuppressor:
    """
    Get or create a noise suppressor instance for the given session.
    Uses caching to avoid recreating instances unnecessarily.

    Args:
        session_id: Session identifier for logging and caching

    Returns:
        NoiseSuppressor instance
    """
    global _noise_suppressor_cache

    # Use session_id as cache key, fallback to "default"
    cache_key = session_id or "default"

    # Return cached instance if available
    if cache_key in _noise_suppressor_cache:
        return _noise_suppressor_cache[cache_key]

    # Create new instance and cache it
    suppressor = NoiseSuppressor(session_id=session_id)
    _noise_suppressor_cache[cache_key] = suppressor

    return suppressor