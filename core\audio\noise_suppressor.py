import numpy as np
import time
import os
import tempfile
import wave
import subprocess
from pathlib import Path
# import noisereduce as nr  # Commented out for Docker RNNoise implementation
from core.config.interrupt_config import get_interrupt_config
from core.logging.logger_config import get_module_logger

# Global instance cache for noise suppressors
_noise_suppressor_cache = {}

class NoiseSuppressionConfig:
    """Configuration object for noise suppression settings"""
    def __init__(self, interrupt_config=None):
        if interrupt_config:
            self.enabled = interrupt_config.global_settings.enable_noise_reduction
            self.noise_strength = interrupt_config.global_settings.noise_reduction_strength
            self.bandpass_low = interrupt_config.global_settings.bandpass_low_freq
            self.bandpass_high = interrupt_config.global_settings.bandpass_high_freq
        else:
            # Fallback defaults
            self.enabled = True
            self.noise_strength = 0.6
            self.bandpass_low = 300.0
            self.bandpass_high = 3400.0

class NoiseSuppressor:
    def __init__(self, session_id=None, interrupt_config=None):
        # Use provided interrupt_config or get default
        cfg = interrupt_config or get_interrupt_config()
        self.config = NoiseSuppressionConfig(cfg)
        self.enabled = self.config.enabled
        self.logger = get_module_logger("noise_suppressor", session_id=session_id)
        self.session_id = session_id

        # Docker RNNoise configuration
        self.rnnoise_container = "voice_agents_rnnoise"
        self.shared_audio_path = Path("./temp_audio")  # Shared volume path
        self.use_docker_rnnoise = True  # Set to False to use fallback noisereduce

        # Ensure shared audio directory exists
        self.shared_audio_path.mkdir(exist_ok=True)

        if self.enabled:
            try:
                # Test Docker RNNoise container availability
                if self.use_docker_rnnoise:
                    # Test if Docker container is running
                    result = subprocess.run(
                        ["docker", "ps", "--filter", f"name={self.rnnoise_container}", "--format", "{{.Names}}"],
                        capture_output=True, text=True, timeout=10
                    )
                    if self.rnnoise_container in result.stdout:
                        self.logger.info(f"Docker RNNoise container '{self.rnnoise_container}' is running")
                    else:
                        self.logger.warning(f"Docker RNNoise container '{self.rnnoise_container}' not found")
                        # Don't disable - container might start later
                else:
                    # Fallback to noisereduce (commented out for now)
                    # test_audio = np.random.randn(1000).astype(np.float32)
                    # _ = nr.reduce_noise(y=test_audio, sr=16000)
                    self.logger.info("Using fallback noise reduction (disabled)")
                    self.enabled = False
                    self.config.enabled = False
            except Exception as e:
                self.logger.warning(f"Could not verify Docker container status: {e}")
                # Don't disable - try processing anyway

    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using Docker RNNoise service for noise suppression"""
        if not self.enabled:
            return audio_samples, {"noise_suppression_applied": False}

        if self.use_docker_rnnoise:
            return self._process_with_docker_rnnoise(audio_samples, sample_rate)
        else:
            return self._process_with_fallback(audio_samples, sample_rate)

    def _process_with_docker_rnnoise(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using Docker RNNoise container with direct subprocess calls"""
        try:
            start_time = time.perf_counter()

            # Convert to int16 if needed
            if audio_samples.dtype != np.int16:
                audio_samples = audio_samples.astype(np.int16)

            # Create unique filenames in shared volume
            import uuid
            session_id = str(uuid.uuid4())[:8]
            input_filename = f"input_{session_id}.wav"
            output_filename = f"output_{session_id}.wav"

            input_path = self.shared_audio_path / input_filename
            output_path = self.shared_audio_path / output_filename

            try:
                # Write input audio to WAV file in shared volume
                with wave.open(str(input_path), 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_samples.tobytes())

                # Call rnnoise-cli in Docker container
                processed_samples = self._call_docker_rnnoise(input_filename, output_filename)

                if processed_samples is not None:
                    proc_time = (time.perf_counter() - start_time) * 1000

                    return processed_samples, {
                        "noise_suppression_applied": True,
                        "processing_time_ms": proc_time,
                        "frame_size": len(audio_samples),
                        "method": "docker_rnnoise_cli",
                        "container": self.rnnoise_container
                    }
                else:
                    self.logger.warning("Docker RNNoise processing failed, returning original audio")
                    return audio_samples, {"noise_suppression_applied": False, "error": "processing_failed"}

            finally:
                # Clean up temporary files
                for path in [input_path, output_path]:
                    if path.exists():
                        path.unlink()

        except Exception as e:
            self.logger.error(f"Docker RNNoise processing error: {e}", exc_info=True)
            return audio_samples, {"noise_suppression_applied": False, "error": str(e)}

    def _call_docker_rnnoise(self, input_filename: str, output_filename: str) -> np.ndarray:
        """Call rnnoise-cli directly in Docker container using subprocess"""
        try:
            # Paths inside the container (shared volume is mounted at /shared_audio)
            container_input_path = f"/shared_audio/{input_filename}"
            container_output_path = f"/shared_audio/{output_filename}"

            # Execute rnnoise-cli inside the Docker container
            cmd = [
                "docker", "exec", self.rnnoise_container,
                "rnnoise-cli", container_input_path, container_output_path
            ]

            self.logger.debug(f"Executing: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                self.logger.error(f"rnnoise-cli failed: {result.stderr}")
                return None

            # Read the processed audio from shared volume
            output_path = self.shared_audio_path / output_filename
            if not output_path.exists():
                self.logger.error(f"Output file not created: {output_path}")
                return None

            try:
                with wave.open(str(output_path), 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                    processed_samples = np.frombuffer(frames, dtype=np.int16)

                self.logger.info(f"Successfully processed audio with rnnoise-cli")
                return processed_samples

            except Exception as e:
                self.logger.error(f"Error reading processed audio: {e}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.error("rnnoise-cli processing timeout")
            return None
        except Exception as e:
            self.logger.error(f"Error calling Docker rnnoise-cli: {e}")
            return None

    def _process_with_fallback(self, audio_samples: np.ndarray, sample_rate: int):
        """Fallback processing method (currently disabled)"""
        self.logger.warning("Fallback noise reduction is disabled")
        return audio_samples, {"noise_suppression_applied": False, "method": "fallback_disabled"}

    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int):
        """Async interface for compatibility with VAD system"""
        return self.process_audio_sync(audio_samples, sample_rate)


def get_noise_suppressor(session_id: str = None, interrupt_config=None) -> NoiseSuppressor:
    """
    Get or create a noise suppressor instance for the given session.
    Uses caching to avoid recreating instances unnecessarily.

    Args:
        session_id: Session identifier for logging and caching
        interrupt_config: Optional interrupt configuration to use instead of default

    Returns:
        NoiseSuppressor instance
    """
    global _noise_suppressor_cache

    # Use session_id as cache key, fallback to "default"
    cache_key = session_id or "default"

    # If interrupt_config is provided, create a new instance (don't cache workflow-specific configs)
    if interrupt_config:
        return NoiseSuppressor(session_id=session_id, interrupt_config=interrupt_config)

    # Return cached instance if available
    if cache_key in _noise_suppressor_cache:
        return _noise_suppressor_cache[cache_key]

    # Create new instance and cache it
    suppressor = NoiseSuppressor(session_id=session_id)
    _noise_suppressor_cache[cache_key] = suppressor

    return suppressor