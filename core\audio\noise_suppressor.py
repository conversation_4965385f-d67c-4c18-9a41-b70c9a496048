import numpy as np
import time
import noisereduce as nr
from core.config.interrupt_config import get_interrupt_config
from core.logging.logger_config import get_module_logger

# Global instance cache for noise suppressors
_noise_suppressor_cache = {}

class NoiseSuppressionConfig:
    """Configuration object for noise suppression settings"""
    def __init__(self, interrupt_config=None):
        if interrupt_config:
            self.enabled = interrupt_config.global_settings.enable_noise_reduction
            self.noise_strength = interrupt_config.global_settings.noise_reduction_strength
            self.bandpass_low = interrupt_config.global_settings.bandpass_low_freq
            self.bandpass_high = interrupt_config.global_settings.bandpass_high_freq
        else:
            # Fallback defaults
            self.enabled = True
            self.noise_strength = 0.6
            self.bandpass_low = 300.0
            self.bandpass_high = 3400.0

class NoiseSuppressor:
    def __init__(self, session_id=None):
        cfg = get_interrupt_config()
        self.config = NoiseSuppressionConfig(cfg)
        self.enabled = self.config.enabled
        self.logger = get_module_logger("noise_suppressor", session_id=session_id)
        self.denoiser = None
        self.frame_size = 480  # 10ms @48kHz (RNNoise's optimal frame size)

        if self.enabled:
            try:
                # Pre-initialize with optimal frame size
                self.denoiser = RNNoise(frame_size=self.frame_size)
                self.logger.info("RNNoise initialized for real-time processing")
            except Exception as e:
                self.enabled = False
                self.config.enabled = False
                self.logger.error(f"RNNoise init failed: {e}")

    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int):
        """Direct synchronous processing for minimal latency"""
        if not self.enabled or not self.denoiser:
            return audio_samples, {"noise_suppression_applied": False}
        
        # Handle sample rate mismatch
        if sample_rate != 48000:
            return audio_samples, {
                "noise_suppression_applied": False,
                "warning": f"RNNoise requires 48kHz (got {sample_rate}Hz)"
            }

        try:
            # Convert to int16 if needed
            if audio_samples.dtype != np.int16:
                audio_samples = audio_samples.astype(np.int16)
            
            # Process directly - RNNoise handles chunking internally
            start_time = time.perf_counter()
            denoised = self.denoiser.filter(audio_samples)
            proc_time = (time.perf_counter() - start_time) * 1000
            
            return denoised, {
                "noise_suppression_applied": True,
                "processing_time_ms": proc_time,
                "frame_size": len(audio_samples)
            }
        except Exception as e:
            self.logger.error(f"RNNoise error: {e}", exc_info=True)
            return audio_samples, {"noise_suppression_applied": False}

    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int):
        """Async interface for compatibility with VAD system"""
        return self.process_audio_sync(audio_samples, sample_rate)


def get_noise_suppressor(session_id: str = None) -> NoiseSuppressor:
    """
    Get or create a noise suppressor instance for the given session.
    Uses caching to avoid recreating instances unnecessarily.

    Args:
        session_id: Session identifier for logging and caching

    Returns:
        NoiseSuppressor instance
    """
    global _noise_suppressor_cache

    # Use session_id as cache key, fallback to "default"
    cache_key = session_id or "default"

    # Return cached instance if available
    if cache_key in _noise_suppressor_cache:
        return _noise_suppressor_cache[cache_key]

    # Create new instance and cache it
    suppressor = NoiseSuppressor(session_id=session_id)
    _noise_suppressor_cache[cache_key] = suppressor

    return suppressor