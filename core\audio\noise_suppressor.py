import numpy as np
import time
import noisereduce as nr
from core.config.interrupt_config import get_interrupt_config
from core.logging.logger_config import get_module_logger

# Global instance cache for noise suppressors
_noise_suppressor_cache = {}

class NoiseSuppressionConfig:
    """Configuration object for noise suppression settings"""
    def __init__(self, interrupt_config=None):
        if interrupt_config:
            self.enabled = interrupt_config.global_settings.enable_noise_reduction
            self.noise_strength = interrupt_config.global_settings.noise_reduction_strength
            self.bandpass_low = interrupt_config.global_settings.bandpass_low_freq
            self.bandpass_high = interrupt_config.global_settings.bandpass_high_freq
        else:
            # Fallback defaults
            self.enabled = True
            self.noise_strength = 0.6
            self.bandpass_low = 300.0
            self.bandpass_high = 3400.0

class NoiseSuppressor:
    def __init__(self, session_id=None):
        cfg = get_interrupt_config()
        self.config = NoiseSuppressionConfig(cfg)
        self.enabled = self.config.enabled
        self.logger = get_module_logger("noise_suppressor", session_id=session_id)
        self.session_id = session_id

        if self.enabled:
            try:
                # Test noisereduce availability
                test_audio = np.random.randn(1000).astype(np.float32)
                _ = nr.reduce_noise(y=test_audio, sr=16000)
                self.logger.info("noisereduce is available for noise suppression")
            except Exception as e:
                self.enabled = False
                self.config.enabled = False
                self.logger.error(f"noisereduce init failed: {e}")
                self.logger.info("Noise suppression disabled - noisereduce not available")

    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using noisereduce for noise suppression"""
        if not self.enabled:
            return audio_samples, {"noise_suppression_applied": False}

        try:
            start_time = time.perf_counter()

            # Convert to float32 for noisereduce (it expects float values)
            if audio_samples.dtype != np.float32:
                # Convert from int16 to float32 in range [-1, 1]
                audio_float = audio_samples.astype(np.float32) / 32768.0
            else:
                audio_float = audio_samples

            # Apply noise reduction using noisereduce
            # Use stationary noise reduction (faster, good for consistent background noise)
            reduced_noise = nr.reduce_noise(
                y=audio_float,
                sr=sample_rate,
                stationary=True,  # Assume stationary noise for better performance
                prop_decrease=self.config.noise_strength  # Use configured noise strength
            )

            # Convert back to original dtype
            if audio_samples.dtype == np.int16:
                # Convert back to int16
                processed_samples = (reduced_noise * 32768.0).astype(np.int16)
                # Clip to prevent overflow
                processed_samples = np.clip(processed_samples, -32768, 32767)
            else:
                processed_samples = reduced_noise.astype(audio_samples.dtype)

            proc_time = (time.perf_counter() - start_time) * 1000

            return processed_samples, {
                "noise_suppression_applied": True,
                "processing_time_ms": proc_time,
                "frame_size": len(audio_samples),
                "noise_strength": self.config.noise_strength
            }

        except Exception as e:
            self.logger.error(f"noisereduce processing error: {e}", exc_info=True)
            return audio_samples, {"noise_suppression_applied": False}

    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int):
        """Async interface for compatibility with VAD system"""
        return self.process_audio_sync(audio_samples, sample_rate)


def get_noise_suppressor(session_id: str = None) -> NoiseSuppressor:
    """
    Get or create a noise suppressor instance for the given session.
    Uses caching to avoid recreating instances unnecessarily.

    Args:
        session_id: Session identifier for logging and caching

    Returns:
        NoiseSuppressor instance
    """
    global _noise_suppressor_cache

    # Use session_id as cache key, fallback to "default"
    cache_key = session_id or "default"

    # Return cached instance if available
    if cache_key in _noise_suppressor_cache:
        return _noise_suppressor_cache[cache_key]

    # Create new instance and cache it
    suppressor = NoiseSuppressor(session_id=session_id)
    _noise_suppressor_cache[cache_key] = suppressor

    return suppressor