import numpy as np
import time
import os
import tempfile
import wave
import aiohttp
import asyncio
from pathlib import Path
# import noisereduce as nr  # Commented out for Docker RNNoise implementation
from core.config.interrupt_config import get_interrupt_config
from core.logging.logger_config import get_module_logger

# Global instance cache for noise suppressors
_noise_suppressor_cache = {}

class NoiseSuppressionConfig:
    """Configuration object for noise suppression settings"""
    def __init__(self, interrupt_config=None):
        if interrupt_config:
            self.enabled = interrupt_config.global_settings.enable_noise_reduction
            self.noise_strength = interrupt_config.global_settings.noise_reduction_strength
            self.bandpass_low = interrupt_config.global_settings.bandpass_low_freq
            self.bandpass_high = interrupt_config.global_settings.bandpass_high_freq
        else:
            # Fallback defaults
            self.enabled = True
            self.noise_strength = 0.6
            self.bandpass_low = 300.0
            self.bandpass_high = 3400.0

class NoiseSuppressor:
    def __init__(self, session_id=None, interrupt_config=None):
        # Use provided interrupt_config or get default
        cfg = interrupt_config or get_interrupt_config()
        self.config = NoiseSuppressionConfig(cfg)
        self.enabled = self.config.enabled
        self.logger = get_module_logger("noise_suppressor", session_id=session_id)
        self.session_id = session_id

        # Docker RNNoise service configuration
        self.rnnoise_host = os.getenv("RNNOISE_SERVICE_HOST", "localhost")
        self.rnnoise_port = os.getenv("RNNOISE_SERVICE_PORT", "8002")
        self.rnnoise_url = f"http://{self.rnnoise_host}:{self.rnnoise_port}"
        self.use_docker_rnnoise = True  # Set to False to use fallback noisereduce

        if self.enabled:
            try:
                # Test Docker RNNoise service availability
                if self.use_docker_rnnoise:
                    # We'll test the connection in the first process call
                    self.logger.info(f"Docker RNNoise service configured at {self.rnnoise_url}")
                else:
                    # Fallback to noisereduce (commented out for now)
                    # test_audio = np.random.randn(1000).astype(np.float32)
                    # _ = nr.reduce_noise(y=test_audio, sr=16000)
                    self.logger.info("Using fallback noise reduction (disabled)")
                    self.enabled = False
                    self.config.enabled = False
            except Exception as e:
                self.enabled = False
                self.config.enabled = False
                self.logger.error(f"Noise suppressor init failed: {e}")
                self.logger.info("Noise suppression disabled")

    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using Docker RNNoise service for noise suppression"""
        if not self.enabled:
            return audio_samples, {"noise_suppression_applied": False}

        if self.use_docker_rnnoise:
            return self._process_with_docker_rnnoise(audio_samples, sample_rate)
        else:
            return self._process_with_fallback(audio_samples, sample_rate)

    def _process_with_docker_rnnoise(self, audio_samples: np.ndarray, sample_rate: int):
        """Process audio using Docker RNNoise service"""
        try:
            start_time = time.perf_counter()

            # Convert to int16 if needed
            if audio_samples.dtype != np.int16:
                audio_samples = audio_samples.astype(np.int16)

            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as input_file:
                input_path = input_file.name

            try:
                # Write input audio to WAV file
                with wave.open(input_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_samples.tobytes())

                # Send to Docker RNNoise service
                processed_samples = self._call_rnnoise_service(input_path)

                if processed_samples is not None:
                    proc_time = (time.perf_counter() - start_time) * 1000

                    return processed_samples, {
                        "noise_suppression_applied": True,
                        "processing_time_ms": proc_time,
                        "frame_size": len(audio_samples),
                        "method": "docker_rnnoise",
                        "service_url": self.rnnoise_url
                    }
                else:
                    self.logger.warning("Docker RNNoise service failed, returning original audio")
                    return audio_samples, {"noise_suppression_applied": False, "error": "service_failed"}

            finally:
                # Clean up temporary file
                if os.path.exists(input_path):
                    os.unlink(input_path)

        except Exception as e:
            self.logger.error(f"Docker RNNoise processing error: {e}", exc_info=True)
            return audio_samples, {"noise_suppression_applied": False, "error": str(e)}

    def _call_rnnoise_service(self, input_path: str) -> np.ndarray:
        """Call the Docker RNNoise service synchronously"""
        try:
            # Use asyncio to run the async call
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._call_rnnoise_service_async(input_path))
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"Error calling RNNoise service: {e}")
            return None

    async def _call_rnnoise_service_async(self, input_path: str) -> np.ndarray:
        """Call the Docker RNNoise service asynchronously"""
        try:
            # Calculate control level from noise strength (0.0-1.0 -> 0-100)
            control_level = int(self.config.noise_strength * 100)

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                # Prepare the file for upload
                with open(input_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('audio_file', f, filename='audio.wav', content_type='audio/wav')
                    data.add_field('control_level', str(control_level))

                    # Send request to RNNoise service
                    async with session.post(f"{self.rnnoise_url}/process-audio", data=data) as response:
                        if response.status == 200:
                            # Save the processed audio to a temporary file
                            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as output_file:
                                output_path = output_file.name
                                async for chunk in response.content.iter_chunked(8192):
                                    output_file.write(chunk)

                            try:
                                # Read the processed audio
                                with wave.open(output_path, 'rb') as wav_file:
                                    frames = wav_file.readframes(-1)
                                    processed_samples = np.frombuffer(frames, dtype=np.int16)

                                return processed_samples
                            finally:
                                # Clean up output file
                                if os.path.exists(output_path):
                                    os.unlink(output_path)
                        else:
                            self.logger.error(f"RNNoise service returned status {response.status}")
                            return None

        except Exception as e:
            self.logger.error(f"Error in async RNNoise service call: {e}")
            return None

    def _process_with_fallback(self, audio_samples: np.ndarray, sample_rate: int):
        """Fallback processing method (currently disabled)"""
        self.logger.warning("Fallback noise reduction is disabled")
        return audio_samples, {"noise_suppression_applied": False, "method": "fallback_disabled"}

    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int):
        """Async interface for compatibility with VAD system"""
        return self.process_audio_sync(audio_samples, sample_rate)


def get_noise_suppressor(session_id: str = None, interrupt_config=None) -> NoiseSuppressor:
    """
    Get or create a noise suppressor instance for the given session.
    Uses caching to avoid recreating instances unnecessarily.

    Args:
        session_id: Session identifier for logging and caching
        interrupt_config: Optional interrupt configuration to use instead of default

    Returns:
        NoiseSuppressor instance
    """
    global _noise_suppressor_cache

    # Use session_id as cache key, fallback to "default"
    cache_key = session_id or "default"

    # If interrupt_config is provided, create a new instance (don't cache workflow-specific configs)
    if interrupt_config:
        return NoiseSuppressor(session_id=session_id, interrupt_config=interrupt_config)

    # Return cached instance if available
    if cache_key in _noise_suppressor_cache:
        return _noise_suppressor_cache[cache_key]

    # Create new instance and cache it
    suppressor = NoiseSuppressor(session_id=session_id)
    _noise_suppressor_cache[cache_key] = suppressor

    return suppressor