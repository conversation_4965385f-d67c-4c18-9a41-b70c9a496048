"""
RNNoise Service API
A FastAPI service that provides noise suppression using rnnoise-cli in a Linux container.
"""

import os
import subprocess
import tempfile
import uuid
from pathlib import Path
from typing import Optional

import aiofiles
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="RNNoise Service", version="1.0.0")

# Create temp directory for audio processing
TEMP_DIR = Path("/app/temp_audio")
TEMP_DIR.mkdir(exist_ok=True)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "rnnoise"}

@app.get("/test-rnnoise")
async def test_rnnoise():
    """Test if rnnoise-cli is available"""
    try:
        result = subprocess.run(
            ["rnnoise", "--help"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        return {
            "available": result.returncode == 0,
            "version_info": result.stdout if result.returncode == 0 else result.stderr
        }
    except Exception as e:
        return {"available": False, "error": str(e)}

@app.post("/process-audio")
async def process_audio(
    audio_file: UploadFile = File(...),
    control_level: Optional[int] = 50
):
    """
    Process audio file with rnnoise-cli for noise suppression
    
    Args:
        audio_file: WAV audio file to process
        control_level: RNNoise control level (0-100, default 50)
    
    Returns:
        Processed audio file
    """
    if not audio_file.filename.endswith('.wav'):
        raise HTTPException(status_code=400, detail="Only WAV files are supported")
    
    # Generate unique filenames
    session_id = str(uuid.uuid4())
    input_path = TEMP_DIR / f"input_{session_id}.wav"
    output_path = TEMP_DIR / f"output_{session_id}.wav"
    
    try:
        # Save uploaded file
        async with aiofiles.open(input_path, 'wb') as f:
            content = await audio_file.read()
            await f.write(content)
        
        logger.info(f"Processing audio file: {input_path}")
        
        # Process with rnnoise-cli
        # Note: rnnoise-cli syntax may vary, this is a basic implementation
        cmd = [
            "rnnoise",
            str(input_path),
            str(output_path),
            "--control", str(control_level)
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            logger.error(f"RNNoise processing failed: {result.stderr}")
            raise HTTPException(
                status_code=500, 
                detail=f"RNNoise processing failed: {result.stderr}"
            )
        
        # Check if output file was created
        if not output_path.exists():
            raise HTTPException(
                status_code=500,
                detail="RNNoise processing completed but no output file was created"
            )
        
        logger.info(f"Audio processing completed: {output_path}")
        
        # Return the processed file
        return FileResponse(
            path=str(output_path),
            media_type="audio/wav",
            filename=f"processed_{audio_file.filename}",
            headers={"X-Processing-Info": f"control_level={control_level}"}
        )
        
    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Audio processing timeout")
    except Exception as e:
        logger.error(f"Error processing audio: {e}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")
    finally:
        # Cleanup input file
        if input_path.exists():
            input_path.unlink()

@app.post("/process-audio-simple")
async def process_audio_simple(
    audio_file: UploadFile = File(...),
    control_level: Optional[int] = 50
):
    """
    Simplified audio processing endpoint that uses basic noise reduction
    """
    if not audio_file.filename.endswith('.wav'):
        raise HTTPException(status_code=400, detail="Only WAV files are supported")
    
    # Generate unique filenames
    session_id = str(uuid.uuid4())
    input_path = TEMP_DIR / f"input_{session_id}.wav"
    output_path = TEMP_DIR / f"output_{session_id}.wav"
    
    try:
        # Save uploaded file
        async with aiofiles.open(input_path, 'wb') as f:
            content = await audio_file.read()
            await f.write(content)
        
        logger.info(f"Processing audio with simple noise reduction: {input_path}")
        
        # Use sox for basic noise reduction as fallback
        cmd = [
            "sox",
            str(input_path),
            str(output_path),
            "noisered",
            "/dev/null",  # No noise profile, use built-in
            "0.21"  # Noise reduction amount
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            # If sox fails, just copy the original file
            logger.warning(f"Sox processing failed, returning original: {result.stderr}")
            output_path = input_path
        
        logger.info(f"Audio processing completed: {output_path}")
        
        # Return the processed file
        return FileResponse(
            path=str(output_path),
            media_type="audio/wav",
            filename=f"processed_{audio_file.filename}",
            headers={"X-Processing-Info": f"simple_processing=true"}
        )
        
    except Exception as e:
        logger.error(f"Error processing audio: {e}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")
    finally:
        # Cleanup - but not if output_path is the same as input_path
        if input_path.exists() and input_path != output_path:
            input_path.unlink()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
