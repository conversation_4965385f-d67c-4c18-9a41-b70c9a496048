"""
Centralized audio file path management for the AI Voice Agents Platform.
All audio files (TTS, STT, processed audio) should be saved in the temp_audio directory.
"""

import os
from pathlib import Path
from typing import Optional

# Central audio directory - shared with Docker containers
AUDIO_DIR = Path("temp_audio")

def get_audio_dir() -> Path:
    """Get the central audio directory path"""
    # Ensure the directory exists
    AUDIO_DIR.mkdir(exist_ok=True)
    return AUDIO_DIR

def get_tts_audio_path(session_id: str, file_extension: str = "mp3", key: Optional[str] = None) -> str:
    """
    Get standardized path for TTS audio files
    
    Args:
        session_id: Session identifier
        file_extension: Audio file extension (mp3, wav)
        key: Optional cache key for unique naming
        
    Returns:
        Full path to TTS audio file
    """
    audio_dir = get_audio_dir()
    
    if key:
        # Use hashed key for cached files
        import hashlib
        safe_key = hashlib.md5(key.encode()).hexdigest()[:12]
        filename = f"tts_{safe_key}.{file_extension}"
    else:
        filename = f"tts_output_{session_id}.{file_extension}"
    
    return str(audio_dir / filename)

def get_stt_audio_path(timestamp: Optional[int] = None) -> str:
    """
    Get standardized path for STT recorded audio files
    
    Args:
        timestamp: Optional timestamp, uses current time if None
        
    Returns:
        Full path to STT audio file
    """
    import time
    if timestamp is None:
        timestamp = int(time.time())
    
    audio_dir = get_audio_dir()
    filename = f"recorded_audio_{timestamp}.wav"
    return str(audio_dir / filename)

def get_fallback_audio_path(session_id: str) -> str:
    """
    Get standardized path for fallback TTS audio files
    
    Args:
        session_id: Session identifier
        
    Returns:
        Full path to fallback audio file
    """
    audio_dir = get_audio_dir()
    filename = f"tts_fallback_{session_id}.mp3"
    return str(audio_dir / filename)

def get_processed_audio_path(session_id: str, process_type: str = "processed") -> str:
    """
    Get standardized path for processed audio files (noise suppression, etc.)
    
    Args:
        session_id: Session identifier
        process_type: Type of processing (processed, filtered, etc.)
        
    Returns:
        Full path to processed audio file
    """
    audio_dir = get_audio_dir()
    filename = f"{process_type}_audio_{session_id}.wav"
    return str(audio_dir / filename)

def cleanup_old_audio_files(max_age_hours: int = 24):
    """
    Clean up old audio files to prevent disk space issues
    
    Args:
        max_age_hours: Maximum age of files to keep in hours
    """
    import time
    
    audio_dir = get_audio_dir()
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    cleaned_count = 0
    for audio_file in audio_dir.glob("*"):
        if audio_file.is_file():
            file_age = current_time - audio_file.stat().st_mtime
            if file_age > max_age_seconds:
                try:
                    audio_file.unlink()
                    cleaned_count += 1
                except Exception as e:
                    print(f"Warning: Could not delete old audio file {audio_file}: {e}")
    
    if cleaned_count > 0:
        print(f"Cleaned up {cleaned_count} old audio files")

# Initialize audio directory on import
get_audio_dir()
