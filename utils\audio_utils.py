"""
Audio utilities for the Voice Agents Platform.

This module provides audio processing utilities including format conversion,
voice activity detection, audio quality analysis, and audio preprocessing
with integrated structured logging.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import io
import wave
import struct
import math
import os
import numpy as np
from elevenlabs.client import ElevenLabs
import sounddevice as sd


import time
from datetime import datetime
import numpy as np
try:
    import webrtcvad
    _webrtcvad_available = True
except ImportError:
    _webrtcvad_available = False

try:
    import pyaudioanalysis
    _pyaudioanalysis_available = True
except ImportError:
    _pyaudioanalysis_available = False
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)
from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.config.interrupt_config import get_interrupt_config
interrupt_config = get_interrupt_config()

VAD_DEBUG_MODE = os.getenv("VAD_DEBUG_MODE", "false").lower() == "true"
# Module logger
logger = get_module_logger("audio_utils")


class AudioProcessor:
    """Audio processing utilities with logging."""

    def __init__(self):
        self.supported_formats = ['wav', 'mp3', 'flac', 'ogg']
        self.sample_rates = [8000, 16000, 22050, 44100, 48000]

        logger.info(
            "Initialized AudioProcessor (noise suppression handled by dedicated module)",
            action="initialize",
            output_data={
                "supported_formats": self.supported_formats,
                "supported_sample_rates": self.sample_rates
            },
            layer="audio_utils"
        )

    # NOTE: Noise suppression methods moved to core/audio/noise_suppressor.py




    def analyze_audio_properties(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Analyze audio properties like format, duration, sample rate, etc.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with audio properties
        """
        try:
            logger.info(
                "Starting audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    properties = {
                        "format": "wav",
                        "channels": wav_file.getnchannels(),
                        "sample_rate": wav_file.getframerate(),
                        "sample_width": wav_file.getsampwidth(),
                        "frames": wav_file.getnframes(),
                        "duration_seconds": wav_file.getnframes() / wav_file.getframerate(),
                        "size_bytes": len(audio_data)
                    }
                    
                    # Calculate additional metrics
                    properties["bitrate"] = properties["sample_rate"] * properties["sample_width"] * 8 * properties["channels"]
                    properties["is_stereo"] = properties["channels"] == 2
                    properties["is_mono"] = properties["channels"] == 1
                    
            except wave.Error:
                # If not WAV, provide basic analysis
                properties = {
                    "format": "unknown",
                    "size_bytes": len(audio_data),
                    "estimated_duration": len(audio_data) / 32000,  # Rough estimate
                    "analysis_note": "Could not parse as WAV file"
                }
            
            logger.info(
                "Audio analysis completed",
                action="analyze_audio",
                output_data=properties,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Audio analysis completed",
                code=StatusCode.OK,
                outputs=properties,
                meta={"format": properties.get("format", "unknown")}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio analysis failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def detect_voice_activity(self, audio_data: bytes, threshold: float = 0.01, session_id: str = None) -> StateOutput:
        """
        Detect voice activity in audio data.
        
        Args:
            audio_data: Raw audio data
            threshold: Energy threshold for voice detection
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with voice activity detection results
        """
        try:
            logger.info(
                "Starting voice activity detection",
                action="detect_voice_activity",
                input_data={
                    "audio_size_bytes": len(audio_data),
                    "threshold": threshold
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Simple energy-based VAD
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    frames = wav_file.readframes(wav_file.getnframes())
                    sample_width = wav_file.getsampwidth()
                    
                    # Convert to samples
                    if sample_width == 1:
                        samples = struct.unpack(f'{len(frames)}B', frames)
                        samples = [(s - 128) / 128.0 for s in samples]  # Convert to -1 to 1
                    elif sample_width == 2:
                        samples = struct.unpack(f'{len(frames)//2}h', frames)
                        samples = [s / 32768.0 for s in samples]  # Convert to -1 to 1
                    else:
                        raise ValueError(f"Unsupported sample width: {sample_width}")
                    
                    # Calculate energy in windows
                    window_size = 1024
                    windows = []
                    for i in range(0, len(samples), window_size):
                        window = samples[i:i+window_size]
                        energy = sum(s*s for s in window) / len(window)
                        windows.append(energy)
                    
                    # Detect voice activity
                    voice_windows = [e > threshold for e in windows]
                    voice_ratio = sum(voice_windows) / len(voice_windows) if voice_windows else 0
                    
                    result = {
                        "has_voice": voice_ratio > 0.1,  # At least 10% voice activity
                        "voice_ratio": voice_ratio,
                        "total_windows": len(windows),
                        "voice_windows": sum(voice_windows),
                        "average_energy": sum(windows) / len(windows) if windows else 0,
                        "max_energy": max(windows) if windows else 0,
                        "threshold_used": threshold
                    }
                    
            except wave.Error:
                # Fallback: simple amplitude analysis
                if len(audio_data) < 100:
                    result = {
                        "has_voice": False,
                        "voice_ratio": 0.0,
                        "error": "Audio too short for analysis"
                    }
                else:
                    # Very basic analysis on raw bytes
                    avg_amplitude = sum(audio_data) / len(audio_data)
                    result = {
                        "has_voice": avg_amplitude > 50,  # Arbitrary threshold
                        "voice_ratio": min(avg_amplitude / 128.0, 1.0),
                        "average_amplitude": avg_amplitude,
                        "analysis_note": "Fallback analysis on raw audio data"
                    }
            
            logger.info(
                "Voice activity detection completed",
                action="detect_voice_activity",
                output_data=result,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Voice activity: {'detected' if result['has_voice'] else 'not detected'}",
                code=StatusCode.OK,
                outputs=result,
                meta={"has_voice": result["has_voice"]}
            )
            
        except Exception as e:
            logger.error(
                "Error in voice activity detection",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Voice activity detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def validate_audio_quality(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Validate audio quality and provide recommendations.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with quality assessment
        """
        try:
            logger.info(
                "Starting audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            # Get audio properties first
            props_result = self.analyze_audio_properties(audio_data, session_id)
            if props_result.status != StatusType.SUCCESS:
                return props_result
            
            properties = props_result.outputs
            quality_issues = []
            recommendations = []
            quality_score = 100  # Start with perfect score
            
            # Check sample rate
            if properties.get("sample_rate", 0) < 16000:
                quality_issues.append("Low sample rate")
                recommendations.append("Use at least 16kHz sample rate for better quality")
                quality_score -= 20
            
            # Check duration
            duration = properties.get("duration_seconds", 0)
            if duration < 0.5:
                quality_issues.append("Audio too short")
                recommendations.append("Provide at least 0.5 seconds of audio")
                quality_score -= 30
            elif duration > 30:
                quality_issues.append("Audio very long")
                recommendations.append("Consider splitting long audio into smaller chunks")
                quality_score -= 10
            
            # Check channels
            if properties.get("channels", 0) > 2:
                quality_issues.append("Too many channels")
                recommendations.append("Use mono or stereo audio")
                quality_score -= 15
            
            # Check file size
            size_mb = properties.get("size_bytes", 0) / (1024 * 1024)
            if size_mb > 10:
                quality_issues.append("Large file size")
                recommendations.append("Consider compressing audio or reducing quality")
                quality_score -= 10
            
            quality_score = max(quality_score, 0)  # Don't go below 0
            
            result = {
                "quality_score": quality_score,
                "quality_grade": "A" if quality_score >= 90 else "B" if quality_score >= 70 else "C" if quality_score >= 50 else "D",
                "issues": quality_issues,
                "recommendations": recommendations,
                "properties": properties,
                "is_acceptable": quality_score >= 50
            }
            
            logger.info(
                "Audio quality validation completed",
                action="validate_quality",
                output_data={
                    "quality_score": quality_score,
                    "quality_grade": result["quality_grade"],
                    "issues_count": len(quality_issues)
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Audio quality: {result['quality_grade']} (Score: {quality_score})",
                code=StatusCode.OK,
                outputs=result,
                meta={"quality_score": quality_score}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio quality validation failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


# ========== SIMPLE VAD HELPER FUNCTION ==========

def enhanced_vad_check(audio_samples: np.ndarray, sample_rate: int = 16000,
                      threshold: float = 0.05, apply_noise_suppression: bool = False) -> Dict[str, Any]:
    """
    🎯 SIMPLE ENHANCED VAD CHECK

    Args:
        audio_samples: Audio samples (should already be processed by noise suppressor)
        sample_rate: Sample rate (default: 16000)
        threshold: VAD threshold (default: 0.05)
        apply_noise_suppression: Ignored - samples should already be processed

    Returns:
        Dict with VAD results and metrics
    """
    try:
        # Calculate basic metrics
        energy = np.mean(audio_samples.astype(np.float64) ** 2)
        rms_energy = np.sqrt(max(0, energy))  # Ensure non-negative for sqrt

        # Zero-crossing rate
        zero_crossings = np.where(np.diff(np.sign(audio_samples)))[0]
        zcr = len(zero_crossings) / len(audio_samples)

        # Spectral centroid
        fft = np.fft.rfft(audio_samples)
        magnitude = np.abs(fft)
        freqs = np.fft.rfftfreq(len(audio_samples), 1/sample_rate)
        spectral_centroid = np.sum(freqs * magnitude) / (np.sum(magnitude) + 1e-10)

        # Enhanced decision logic - AGGRESSIVE fan noise rejection
        energy_check = energy > threshold
        zcr_check = 0.03 <= zcr <= 0.4  # Reasonable ZCR range for human speech
        spectral_check = 200 <= spectral_centroid <= 4000  # Broader human voice range

        # Additional checks for fan noise rejection
        significant_energy_check = energy > (threshold * 50)  # Much higher threshold for fan environments

        # Fan noise typically has very consistent energy - check for variation
        # Calculate energy variation within the sample
        chunk_size = len(audio_samples) // 10  # Divide into 10 chunks
        if chunk_size > 0:
            chunk_energies = []
            for i in range(0, len(audio_samples) - chunk_size, chunk_size):
                chunk = audio_samples[i:i + chunk_size]
                chunk_energy = np.mean(chunk.astype(np.float64) ** 2)
                chunk_energies.append(chunk_energy)

            if len(chunk_energies) > 1:
                energy_std = np.std(chunk_energies)
                energy_variation = energy_std / (np.mean(chunk_energies) + 1e-10)
                # Human speech has more energy variation than fan noise (stricter for fan environments)
                variation_check = energy_variation > 0.5  # At least 50% variation (much stricter)
            else:
                variation_check = True
        else:
            variation_check = True

        # Spectral complexity check - human speech has more complex harmonics
        fft = np.fft.rfft(audio_samples)
        magnitude = np.abs(fft)
        # Count significant frequency peaks (indicates harmonic complexity)
        peak_threshold = np.max(magnitude) * 0.05  # 5% of max magnitude
        significant_peaks = np.sum(magnitude > peak_threshold)
        complexity_check = significant_peaks > 3  # At least 3 significant peaks

        # AGGRESSIVE fan-specific rejection (based on your diagnostic data)
        # Your fan characteristics: ZCR 0.04-0.08, Centroid 1600-2000Hz, Energy 5K-350K
        fan_zcr_pattern = 0.04 <= zcr <= 0.09  # Your fan's ZCR range (expanded)
        fan_spectral_pattern = 1600 <= spectral_centroid <= 2000  # Your fan's spectral range
        fan_energy_pattern = 5000 <= energy <= 400000  # Your fan's energy range (expanded)

        # Additional temporal consistency check for fan noise
        # Fan noise has very consistent spectral characteristics over time
        if len(audio_samples) > sample_rate // 10:  # At least 100ms of audio
            # Analyze spectral consistency across the sample
            mid_point = len(audio_samples) // 2
            first_half = audio_samples[:mid_point]
            second_half = audio_samples[mid_point:]

            # Calculate spectral centroids for each half
            fft1 = np.fft.rfft(first_half)
            fft2 = np.fft.rfft(second_half)
            freqs = np.fft.rfftfreq(len(first_half), 1/sample_rate)

            mag1 = np.abs(fft1)
            mag2 = np.abs(fft2)

            centroid1 = np.sum(freqs * mag1) / (np.sum(mag1) + 1e-10)
            centroid2 = np.sum(freqs * mag2) / (np.sum(mag2) + 1e-10)

            # Fan noise has very consistent spectral centroid
            spectral_consistency = abs(centroid1 - centroid2) / max(centroid1, centroid2, 1)

            # If spectral centroid is too consistent, it's likely fan noise
            too_consistent = spectral_consistency < 0.05  # Less than 5% variation
        else:
            too_consistent = False

        # If it matches fan pattern OR is too spectrally consistent, REJECT IT
        if (fan_zcr_pattern and fan_spectral_pattern and fan_energy_pattern) or too_consistent:
            # This is definitely fan noise - REJECT regardless of other checks
            fan_rejection_check = False  # Force rejection for fan-like signals
        else:
            fan_rejection_check = True  # Not fan-like, allow normal processing

        # Use scoring system with fan-specific rejection
        score = 0
        if energy_check: score += 1
        if zcr_check: score += 2  # ZCR is very important for speech
        if spectral_check: score += 2  # Spectral centroid is very important
        if significant_energy_check: score += 1
        if variation_check: score += 1  # Helps reject fan noise
        if complexity_check: score += 1  # Helps reject simple tones
        if fan_rejection_check: score += 2  # Critical for fan environments

        # Need at least 7 out of 10 points, with critical checks passing
        has_voice = (score >= 7 and zcr_check and spectral_check and
                    significant_energy_check and fan_rejection_check)

        return {
            "has_voice": bool(has_voice),
            "energy": float(energy),
            "rms_energy": float(rms_energy),
            "zero_crossing_rate": float(zcr),
            "spectral_centroid": float(spectral_centroid),
            "checks": {
                "energy_check": energy_check,
                "zcr_check": zcr_check,
                "spectral_check": spectral_check,
                "significant_energy_check": significant_energy_check,
                "variation_check": variation_check,
                "complexity_check": complexity_check,
                "fan_rejection_check": fan_rejection_check
            },
            "energy_variation": energy_variation if 'energy_variation' in locals() else 0.0,
            "spectral_peaks": significant_peaks if 'significant_peaks' in locals() else 0,
            "vad_score": score,
            "fan_pattern_detected": fan_zcr_pattern and fan_spectral_pattern and fan_energy_pattern,
            "spectral_consistency": spectral_consistency if 'spectral_consistency' in locals() else 0.0,
            "too_consistent": too_consistent if 'too_consistent' in locals() else False
        }

    except Exception as e:
        logger.warning(f"Enhanced VAD failed: {e}")
        return {
            "has_voice": bool(np.mean(audio_samples ** 2) > threshold),
            "energy": float(np.mean(audio_samples ** 2)),
            "error": str(e)
        }


def interrupt_vad_check(audio_samples: np.ndarray, sample_rate: int = 16000,
                       threshold: float = 10.0) -> Dict[str, Any]:
    """
    🎯 SPECIALIZED INTERRUPT VAD CHECK
    ==================================

    Ultra-conservative VAD specifically for interrupt detection.
    Designed to minimize false positives in noisy environments.
    """
    try:
        if len(audio_samples) == 0:
            return {"has_voice": False, "energy": 0, "reason": "empty_audio"}

        # Calculate basic energy
        energy = np.mean(audio_samples.astype(np.float64) ** 2)

        # ULTRA-CONSERVATIVE CHECKS FOR INTERRUPTS

        # 1. Energy must be VERY high (much higher than fan noise)
        energy_check = energy > threshold

        # 2. Energy must be SIGNIFICANTLY above background
        # For interrupts, require 100x above threshold (vs 50x for normal VAD)
        significant_energy_check = energy > (threshold * 100)

        # 3. Must have speech-like characteristics
        zcr = np.mean(np.abs(np.diff(np.sign(audio_samples)))) / 2.0
        zcr_check = 0.05 <= zcr <= 0.25  # Stricter ZCR range for interrupts

        # 4. Must be in clear speech frequency range
        fft = np.fft.rfft(audio_samples)
        magnitude = np.abs(fft)
        freqs = np.fft.rfftfreq(len(audio_samples), 1/sample_rate)

        if np.sum(magnitude) > 0:
            spectral_centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
        else:
            spectral_centroid = 0

        # Very strict spectral range for interrupts (clear human speech only)
        spectral_check = 500 <= spectral_centroid <= 2500

        # 5. Must have enough spectral complexity (not simple tones)
        peak_threshold = np.max(magnitude) * 0.1
        significant_peaks = np.sum(magnitude > peak_threshold)
        complexity_check = significant_peaks > 5

        # ALL CHECKS MUST PASS for interrupt detection
        has_voice = (energy_check and significant_energy_check and zcr_check and
                    spectral_check and complexity_check)

        return {
            "has_voice": has_voice,
            "energy": float(energy),
            "zero_crossing_rate": float(zcr),
            "spectral_centroid": float(spectral_centroid),
            "spectral_peaks": int(significant_peaks),
            "threshold_used": float(threshold),
            "checks": {
                "energy_check": energy_check,
                "significant_energy_check": significant_energy_check,
                "zcr_check": zcr_check,
                "spectral_check": spectral_check,
                "complexity_check": complexity_check
            },
            "reason": "interrupt_vad" if has_voice else "rejected_by_interrupt_vad"
        }

    except Exception as e:
        logger.warning(f"Interrupt VAD failed: {e}")
        return {
            "has_voice": False,
            "energy": 0,
            "error": str(e),
            "reason": "error"
        }


# Convenience functions for easy access
def analyze_audio(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio analysis."""
    processor = AudioProcessor()
    return processor.analyze_audio_properties(audio_data, session_id)


def detect_voice_activity(audio_data: bytes, threshold: float = 0.01, session_id: str = None) -> StateOutput:
    """Convenience function for voice activity detection."""
    processor = AudioProcessor()
    return processor.detect_voice_activity(audio_data, threshold, session_id)


def validate_audio_quality(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio quality validation."""
    processor = AudioProcessor()
    return processor.validate_audio_quality(audio_data, session_id)


# def synthesize_fallback_audio(text, session_id="fallback"):
#     try:
#         api_key = os.getenv("ELEVENLABS_API_KEY")
#         client = ElevenLabs(api_key=api_key)
#         voice_id = "EXAVITQu4vr4xnSDxMaL"  # Default voice
#         audio_path = os.path.abspath(f"tts_fallback_{session_id}.mp3")
#         audio_gen = client.text_to_speech.convert(
#             text=text,
#             voice_id=voice_id,
#             model_id="eleven_multilingual_v2",
#             output_format="mp3_44100_128"
#         )
#         with open(audio_path, "wb") as f:
#             for chunk in audio_gen:
#                 f.write(chunk)
#         return audio_path
#     except Exception as e:
#         print(f"[Fallback TTS] Could not generate fallback audio: {e}")
#         return None


#fallback using google TTS PROVIDER
def synthesize_fallback_audio(text, session_id="fallback"):
    try:
        from google.cloud import texttospeech
        import os

        # Create client
        client = texttospeech.TextToSpeechClient()

        # Set input text
        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Use default fallback voice (female, en-US)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.FEMALE
        )

        # Select audio format
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Generate speech
        response = client.synthesize_speech(
            input=synthesis_input, voice=voice, audio_config=audio_config
        )

        # Save to file
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
        audio_path = os.path.join(project_root, f"tts_fallback_{session_id}.mp3")

        with open(audio_path, "wb") as out:
            out.write(response.audio_content)

        return audio_path
    except Exception as e:
        print(f"[Fallback TTS] Could not generate fallback audio: {e}")
        return None



###############################         VAD DETECT USER AUDIO         ##################3##

async def record_microphone_audio_vad(sample_rate=16000, device_index=None, silence_duration=1.5, max_recording=15):
    """Record audio from microphone with DEDICATED NOISE SUPPRESSOR MODULE, stop when user stops speaking (VAD endpointing).

    Returns:
        str: Path to recorded audio file if speech was detected
        None: If no meaningful speech was detected
    """
    # 🎯 Initialize noise suppressor module
    from core.audio.noise_suppressor import get_noise_suppressor
    noise_suppressor = get_noise_suppressor("recording_session")

    # Optimized VAD settings for lower latency
    chunk_duration = 0.05  # 50ms chunks for faster response (reduced from 100ms)
    chunk_size = int(sample_rate * chunk_duration)
    silence_chunks = int(silence_duration / chunk_duration)
    max_chunks = int(max_recording / chunk_duration)

    # Get VAD threshold from configuration
    interrupt_config = get_interrupt_config()
    vad_threshold = interrupt_config.global_settings.vad_threshold if interrupt_config else 0.05

    print("🎤 [MIC] Recording with DEDICATED NOISE SUPPRESSOR MODULE... Speak now!")
    print(f"🔧 [NOISE SUPPRESSOR] Enabled: {noise_suppressor.config.enabled}, "
          f"Strength: {noise_suppressor.config.noise_strength}, "
          f"Bandpass: {noise_suppressor.config.bandpass_low}-{noise_suppressor.config.bandpass_high}Hz, "
          f"VAD Threshold: {vad_threshold}")

    audio_buffer = []
    silence_counter = 0
    speech_detected = False  # Track if ANY speech was detected
    total_speech_chunks = 0  # Count total speech chunks for quality validation
    consecutive_speech_chunks = 0  # Track consecutive speech for validation
    max_consecutive_speech = 0  # Track longest consecutive speech sequence
    energy_levels = []  # Track energy levels for debugging

    with sd.InputStream(samplerate=sample_rate, channels=1, dtype='int16', device=device_index, blocksize=chunk_size) as stream:
        for chunk_idx in range(max_chunks):
            chunk, _ = stream.read(chunk_size)
            chunk_bytes = chunk.tobytes()
            audio_buffer.append(chunk_bytes)

            # Convert to numpy array for processing
            chunk_array = np.frombuffer(chunk_bytes, dtype=np.int16)

            # 🎯 STEP 1: Apply noise suppressor module (threaded for performance)
            filtered_chunk, suppression_info = await noise_suppressor.process_audio_async(
                chunk_array,
                sample_rate
            )

            # 🎯 STEP 2: Enhanced VAD on filtered audio
            vad_result = enhanced_vad_check(
                filtered_chunk,
                sample_rate,
                vad_threshold,
                apply_noise_suppression=False  # Already applied by module
            )

            # Extract results
            is_speech = vad_result.get('has_voice', False)
            audio_energy = vad_result.get('rms_energy', 0)

            energy_levels.append(audio_energy)

            # Debug info every 30 chunks (3 seconds with 100ms chunks)
            if chunk_idx % 30 == 0 and chunk_idx > 0:
                suppression_time = suppression_info.get('processing_time_ms', 0)
                print(f"🔧 [NOISE SUPPRESSOR] Energy: {audio_energy:.1f}, Speech: {is_speech}, "
                      f"Suppression: {suppression_time:.1f}ms, "
                      f"Filtered: {suppression_info.get('noise_suppression_applied', False)}")

            if is_speech:
                speech_detected = True
                total_speech_chunks += 1
                consecutive_speech_chunks += 1
                max_consecutive_speech = max(max_consecutive_speech, consecutive_speech_chunks)
                silence_counter = 0
            else:
                consecutive_speech_chunks = 0
                silence_counter += 1

            if silence_counter > silence_chunks:
                if speech_detected:
                    print("🛑 Silence detected, stopping recording.")
                else:
                    print("🔇 No speech detected during recording period.")
                break

    # Enhanced speech quality validation with noise suppression metrics
    total_chunks = len(audio_buffer)
    speech_ratio = total_speech_chunks / total_chunks if total_chunks > 0 else 0

    # Optimized requirements for faster response (50ms chunks)
    min_speech_chunks = 2   # Further reduced for 50ms chunks (2 chunks = 100ms minimum)
    min_speech_ratio = 0.10  # Lower ratio for faster acceptance
    min_consecutive_speech = 3  # Reduced from 4 - accept speech faster

    if (not speech_detected or
        total_speech_chunks < min_speech_chunks or
        speech_ratio < min_speech_ratio or
        max_consecutive_speech < min_consecutive_speech):

        # Enhanced debug information
        avg_energy = sum(energy_levels) / len(energy_levels) if energy_levels else 0
        max_energy = max(energy_levels) if energy_levels else 0

        print(f"❌ No meaningful speech detected (ENHANCED VAD):")
        print(f"   📊 Speech chunks: {total_speech_chunks}/{total_chunks} ({speech_ratio:.1%})")
        print(f"   🔗 Max consecutive speech: {max_consecutive_speech} chunks ({max_consecutive_speech * 30}ms)")
        print(f"   🔊 Audio energy: avg={avg_energy:.1f}, max={max_energy:.1f}")
        print(f"   🎯 Enhanced requirements: ≥{min_speech_chunks} chunks, ≥{min_speech_ratio:.0%} ratio, ≥{min_consecutive_speech} consecutive")
        print(f"   🔧 Noise suppression: {noise_suppressor.config.enabled}, "
              f"Bandpass: {noise_suppressor.config.bandpass_low}-{noise_suppressor.config.bandpass_high}Hz")
        print(f"   💡 Try speaking louder, clearer, or closer to the microphone.")
        return None

    # Only create file if meaningful speech was detected
    timestamp = int(time.time())
    audio_filename = f'recorded_audio_{timestamp}.wav'
    audio_data = b''.join(audio_buffer)
    with wave.open(audio_filename, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data)

    print(f"✅ [SPEECH DETECTED] Saved recorded audio to: {audio_filename}")
    print(f"   📊 Quality: {total_speech_chunks} speech chunks ({speech_ratio:.1%} speech ratio)")
    print(f"   🔗 Max consecutive speech: {max_consecutive_speech} chunks ({max_consecutive_speech * 30}ms)")
    return audio_filename
def detect_voice_activity(self, audio_data: bytes, threshold: float = None, session_id: str = None, use_pyaudioanalysis: bool = None) -> StateOutput:
        """
        Enhanced: Use webrtcvad, pyAudioAnalysis, or energy-based VAD based on VAD_METHOD toggle.
        """
        if self.interrupt_config and not self.interrupt_config.global_settings.enabled:
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt detection is disabled in config.",
                code=StatusCode.OK,
                outputs={"has_voice": False, "detection_disabled": True},
                meta={"interrupt_detection_enabled": False}
            )
        if threshold is None and self.interrupt_config is not None:
            threshold = self.interrupt_config.global_settings.vad_threshold
        elif threshold is None:
            threshold = 0.05

        # --- VAD method selection from configuration ---
        # Get VAD method from interrupt config, fallback to webrtcvad
        vad_method = self.interrupt_config.global_settings.vad_method.lower() if self.interrupt_config else 'webrtcvad'

        if vad_method == 'webrtcvad' and _webrtcvad_available:
            logger.info("Using webrtcvad for VAD", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
            vad_result = self.vad_with_webrtcvad(audio_data)
            if vad_result is not None:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"Voice activity: {'detected' if vad_result else 'not detected'} (webrtcvad)",
                    code=StatusCode.OK,
                    outputs={
                        "has_voice": vad_result,
                        "vad_method": "webrtcvad"
                    },
                    meta={"has_voice": vad_result}
                )
            else:
                logger.warning("webrtcvad VAD failed, falling back to pyAudioAnalysis or energy-based VAD.", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
        if vad_method == 'pyaudioanalysis' and _pyaudioanalysis_available:
            logger.info("Using pyAudioAnalysis for VAD", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
            vad_result = self.vad_with_pyaudioanalysis(audio_data)
            if vad_result is not None:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"Voice activity: {'detected' if vad_result else 'not detected'} (pyAudioAnalysis)",
                    code=StatusCode.OK,
                    outputs={
                        "has_voice": vad_result,
                        "vad_method": "pyAudioAnalysis"
                    },
                    meta={"has_voice": vad_result}
                )
            else:
                logger.warning("pyAudioAnalysis VAD failed, falling back to energy-based VAD.", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
        # Default: energy-based VAD
        try:
            logger.info(
                "Starting voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data), "threshold": threshold},
                layer="audio_utils",
                session_id=session_id
            )

            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )

            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    sample_rate = wav_file.getframerate()
                    sample_width = wav_file.getsampwidth()
                    channels = wav_file.getnchannels()
                    frames = wav_file.readframes(wav_file.getnframes())

                    # Convert to mono if needed
                    if channels > 1:
                        logger.info("Converting multi-channel audio to mono for VAD", action="detect_voice_activity", layer="audio_utils")
                        # Only keep the first channel
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                            samples = samples[::channels]
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                            samples = samples[::channels]
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")
                    else:
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")

                    # Apply noise suppression pipeline
                    # Step 1: Apply bandpass filter to isolate speech frequencies
                    filtered_samples = self.bandpass_filter(samples, sample_rate)

                    # Step 2: Apply noise reduction if enabled
                    if self.enable_noise_reduction:
                        filtered_samples = self.reduce_noise(
                            filtered_samples,
                            sample_rate,
                            self.noise_reduction_strength
                        )

                    # Step 3: Calculate spectral features for enhanced VAD
                    spectral_features = self.calculate_spectral_features(filtered_samples, sample_rate)

                    filtered_samples = filtered_samples.astype(samples.dtype)

                    # Prepare PCM bytes for webrtcvad (16-bit mono PCM, 8kHz/16kHz/32kHz/48kHz)
                    if sample_width == 2:
                        pcm_bytes = filtered_samples.tobytes()
                    elif sample_width == 1:
                        # Convert 8-bit unsigned to 16-bit signed
                        pcm_bytes = (filtered_samples.astype(np.int16) - 128).tobytes()
                    else:
                        pcm_bytes = filtered_samples.tobytes()

                    # Enhanced VAD with spectral analysis and noise suppression
                    # Calculate energy-based metrics
                    energy = np.mean(filtered_samples ** 2)
                    rms_energy = np.sqrt(energy)
                    peak_amplitude = np.max(np.abs(filtered_samples))

                    # Extract spectral features
                    zcr = spectral_features["zero_crossing_rate"]
                    spectral_centroid = spectral_features["spectral_centroid"]
                    speech_energy_ratio = spectral_features["speech_energy_ratio"]

                    # Enhanced adaptive thresholding using spectral features
                    adaptive_threshold = threshold

                    # Increase threshold for signals that look like TTS/AI voice
                    if zcr < 0.05:  # Very low ZCR suggests synthetic voice
                        adaptive_threshold = threshold * 8.0
                    elif zcr < self.max_zero_crossing_rate:  # Low ZCR suggests possible TTS
                        adaptive_threshold = threshold * 4.0

                    # Check spectral centroid (brightness) - human speech typically 800-2000 Hz
                    if (spectral_centroid < self.min_spectral_centroid or
                        spectral_centroid > self.max_spectral_centroid):
                        adaptive_threshold = threshold * 3.0

                    # Multi-criteria VAD decision
                    energy_check = energy > adaptive_threshold
                    amplitude_check = peak_amplitude > (np.sqrt(adaptive_threshold) * 100)
                    spectral_check = speech_energy_ratio > self.min_speech_energy_ratio
                    zcr_check = zcr > 0.02  # Minimum ZCR for human speech

                    # All criteria must pass for positive VAD decision
                    has_voice = energy_check and amplitude_check and spectral_check and zcr_check
                    voice_ratio = 1.0 if has_voice else 0.0
                    num_windows = 1
                    voice_windows = 1 if has_voice else 0

                    # Debug logging if enabled
                    debug_mode = os.getenv('VAD_DEBUG_MODE', 'false').lower() == 'true'
                    if debug_mode or has_voice:
                        logger.info(
                            f"Enhanced VAD result: energy={energy:.6f}, threshold={adaptive_threshold:.6f}, "
                            f"peak_amp={peak_amplitude:.2f}, zcr={zcr:.3f}, centroid={spectral_centroid:.1f}Hz, "
                            f"speech_ratio={speech_energy_ratio:.3f}, has_voice={has_voice}",
                            action="detect_voice_activity",
                            output_data={
                                "has_voice": has_voice,
                                "energy": energy,
                                "adaptive_threshold": adaptive_threshold,
                                "peak_amplitude": peak_amplitude,
                                "zero_crossing_rate": zcr,
                                "spectral_centroid": spectral_centroid,
                                "speech_energy_ratio": speech_energy_ratio,
                                "energy_check": energy_check,
                                "amplitude_check": amplitude_check,
                                "spectral_check": spectral_check,
                                "zcr_check": zcr_check,
                                "voice_ratio": voice_ratio,
                                "noise_reduction_applied": self.enable_noise_reduction
                            },
                            layer="audio_utils",
                            session_id=session_id
                        )
                    else:
                        logger.debug(
                            "VAD result (energy-based)",
                            action="detect_voice_activity",
                            output_data={"has_voice": has_voice, "voice_ratio": voice_ratio, "num_windows": num_windows},
                            layer="audio_utils",
                            session_id=session_id
                        )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "voice_ratio": voice_ratio,
                            "total_windows": num_windows,
                            "voice_windows": voice_windows,
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
            except wave.Error as e:
                # Suppress expected error for non-WAV input
                logger.info(
                    f"WAV parse failed (expected for raw PCM): {e}",
                    action="detect_voice_activity",
                    layer="audio_utils",
                    session_id=session_id
                )
                # Fallback: simple amplitude/energy analysis
                try:
                    # Try to decode as PCM 16-bit mono
                    samples = np.frombuffer(audio_data, dtype=np.int16)
                    sample_rate = 16000  # Assume 16kHz if unknown

                    # Apply noise suppression pipeline
                    filtered_samples = self.bandpass_filter(samples, sample_rate)

                    # Apply noise reduction if enabled
                    if self.enable_noise_reduction:
                        filtered_samples = self.reduce_noise(
                            filtered_samples,
                            sample_rate,
                            self.noise_reduction_strength
                        )

                    # Calculate spectral features for enhanced VAD
                    spectral_features = self.calculate_spectral_features(filtered_samples, sample_rate)

                    # Basic energy check
                    energy = float(np.mean(filtered_samples ** 2))

                    # Enhanced decision with spectral features
                    zcr = spectral_features["zero_crossing_rate"]
                    speech_energy_ratio = spectral_features["speech_energy_ratio"]

                    # Multi-criteria decision
                    energy_check = energy > threshold
                    spectral_check = speech_energy_ratio > self.min_speech_energy_ratio
                    zcr_check = zcr > 0.02

                    has_voice = bool(energy_check and spectral_check and zcr_check)
                    # Ensure all outputs are native Python types
                    logger.info(
                        "Fallback VAD result (energy-based)",
                        action="detect_voice_activity",
                        output_data={"has_voice": has_voice, "energy": energy},
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "energy": energy,
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
                except Exception as e2:
                    logger.error(
                        "VAD failed on all methods",
                        action="detect_voice_activity",
                        reason=f"{str(e)} | {str(e2)}",
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.ERROR,
                        message=f"Voice activity detection failed: {str(e)} | {str(e2)}",
                        code=StatusCode.INTERNAL_ERROR,
                        outputs={},
                        meta={"error": f"{str(e)} | {str(e2)}"}
                    )
        except Exception as e:
            logger.error(
                "Error in voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Voice activity detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
def vad_with_webrtcvad(self, audio_data: bytes, sample_rate: int = 16000, aggressiveness: int = None) -> Optional[bool]:
        """
        Enhanced webrtcvad implementation with TTS feedback resistance.
        Uses maximum aggressiveness and stricter frame validation to reduce false positives.
        Returns True if speech is detected, False if not, or None if unavailable/error.
        """
        if not _webrtcvad_available:
            logger.warning("webrtcvad not available, cannot use webrtcvad VAD.")
            return None
        try:
            # Use configurable aggressiveness for stricter voice detection
            if aggressiveness is None:
                # Get from interrupt config if available
                aggressiveness = getattr(self.interrupt_config.global_settings, 'webrtc_aggressiveness', 3) if hasattr(self, 'interrupt_config') and self.interrupt_config else 3
            vad = webrtcvad.Vad(aggressiveness)

            # webrtcvad expects 16-bit mono PCM, 10/20/30ms frames
            frame_duration = 30  # ms
            bytes_per_sample = 2
            frame_size = int(sample_rate * frame_duration / 1000) * bytes_per_sample

            # Require more consecutive frames to confirm speech (reduces TTS feedback false positives)
            consecutive_speech_frames = 0
            # Get required consecutive frames from config
            required_consecutive_frames = getattr(self.interrupt_config.global_settings, 'required_consecutive_frames', 5) if hasattr(self, 'interrupt_config') and self.interrupt_config else 5
            total_frames = 0
            speech_frames = 0

            for i in range(0, len(audio_data) - frame_size + 1, frame_size):
                frame = audio_data[i:i+frame_size]
                if len(frame) < frame_size:
                    break

                total_frames += 1
                if vad.is_speech(frame, sample_rate):
                    speech_frames += 1
                    consecutive_speech_frames += 1

                    # If we have enough consecutive speech frames, it's likely real speech
                    if consecutive_speech_frames >= required_consecutive_frames:
                        if VAD_DEBUG_MODE:
                            logger.info(
                                f"webrtcvad VAD result: has_voice=True (consecutive frames: {consecutive_speech_frames})",
                                action="vad_with_webrtcvad",
                                layer="audio_utils"
                            )
                        return True
                else:
                    consecutive_speech_frames = 0

            # Calculate speech ratio for additional validation
            speech_ratio = speech_frames / total_frames if total_frames > 0 else 0

            # Require at least 30% speech frames AND some consecutive frames to confirm
            has_voice = speech_ratio >= 0.3 and consecutive_speech_frames >= 2

            if VAD_DEBUG_MODE:
                logger.info(
                    f"webrtcvad VAD result: has_voice={has_voice} "
                    f"(speech_ratio={speech_ratio:.2f}, max_consecutive={consecutive_speech_frames})",
                    action="vad_with_webrtcvad",
                    layer="audio_utils"
                )
            return has_voice

        except Exception as e:
            logger.error(f"webrtcvad VAD failed: {e}", action="vad_with_webrtcvad", layer="audio_utils")
            return None



