# Use Ubuntu as base image for better rnnoise-cli support
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    cmake \
    git \
    pkg-config \
    libasound2-dev \
    libpulse-dev \
    pulseaudio \
    pulseaudio-utils \
    sox \
    libsox-fmt-all \
    ffmpeg \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install rnnoise-cli
RUN pip3 install rnnoise-cli

# Create app directory
WORKDIR /app

# Create temp audio directory
RUN mkdir -p /app/temp_audio

# Copy requirements for the service
COPY requirements.txt .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Install additional dependencies for the RNNoise service
RUN pip3 install fastapi uvicorn aiofiles

# Copy the RNNoise service code
COPY rnnoise_service.py .

# Expose port
EXPOSE 8002

# Configure PulseAudio for headless operation
RUN mkdir -p /etc/pulse
RUN echo "load-module module-null-sink sink_name=dummy" > /etc/pulse/default.pa
RUN echo "set-default-sink dummy" >> /etc/pulse/default.pa

# Start the RNNoise service
CMD ["python3", "-m", "uvicorn", "rnnoise_service:app", "--host", "0.0.0.0", "--port", "8002"]
