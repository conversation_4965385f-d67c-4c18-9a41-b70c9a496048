# Use Ubuntu as base image for rnnoise-cli support
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    sox \
    libsox-fmt-all \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install rnnoise-cli
RUN pip3 install rnnoise-cli

# Create shared volume directory
RUN mkdir -p /shared_audio

# Keep container running
CMD ["tail", "-f", "/dev/null"]
